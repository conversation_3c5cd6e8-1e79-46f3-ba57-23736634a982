/**
 * Storage factory for creating appropriate storage adapters based on platform.
 *
 * This factory automatically detects the runtime environment and creates
 * the most suitable storage adapter for the platform using dependency injection.
 */

import { logger } from '@/lib/logger';
import type { IStorageAdapter, IStorageAdapterFactory, StorageConfig } from './types';
import { getStorageContainer } from './di/StorageContainer';
import { getEnvironmentDetector, DefaultEnvironmentDetector } from './environment/EnvironmentDetector';

// Import adapters that are always available
import { MemoryStorageAdapter } from './adapters/MemoryStorageAdapter';

/**
 * Storage adapter factory implementation
 */
export class StorageAdapterFactory implements IStorageAdapterFactory {
    private static instance: StorageAdapterFactory | null = null;
    private container = getStorageContainer();
    private dependenciesInitialized = false;

    private constructor() {
        // Dependencies will be initialized lazily when first needed
        this.initializeBasicDependencies();
    }

    /**
     * Get singleton instance of the factory
     */
    public static getInstance(): StorageAdapterFactory {
        if (!StorageAdapterFactory.instance) {
            StorageAdapterFactory.instance = new StorageAdapterFactory();
        }
        return StorageAdapterFactory.instance;
    }

    /**
     * Reset the factory (useful for testing)
     */
    public static reset(): void {
        StorageAdapterFactory.instance = null;
    }

    /**
     * Reset dependencies (useful for testing)
     */
    public resetDependencies(): void {
        this.dependenciesInitialized = false;
    }

    /**
     * Create a storage adapter for the current platform
     */
    async createAdapter(config: StorageConfig): Promise<IStorageAdapter> {
        // Ensure dependencies are initialized before creating adapters
        await this.ensureDependenciesInitialized();

        const adapterType = this.getAdapterType();

        logger.debug(`Creating storage adapter: ${adapterType} for store: ${config.storeName}`);

        try {
            const adapter = await this.container.createAdapter(adapterType, config);
            logger.info(`Storage adapter initialized successfully: ${adapterType}`);
            return adapter;
        } catch (error) {
            logger.error(`Failed to initialize storage adapter: ${adapterType}`, error);

            // Fallback to memory storage if the preferred adapter fails
            if (adapterType !== 'memory') {
                logger.warn('Falling back to memory storage adapter');
                const fallbackAdapter = await this.container.createAdapter('memory', config);
                return fallbackAdapter;
            }

            throw error;
        }
    }



    /**
     * Get the best available adapter for the current platform
     */
    getAdapterType(): string {
        const detector = getEnvironmentDetector();
        return detector.getRecommendedStorageAdapter();
    }

    /**
     * Check if a specific adapter type is available
     */
    isAdapterAvailable(adapterType: string): boolean {
        const detector = getEnvironmentDetector();
        const platform = detector.detectPlatform();

        switch (adapterType) {
            case 'browser':
                return platform.isBrowser;

            case 'node':
                return platform.isServer && platform.platform === 'node';

            case 'react-native':
                return platform.isReactNative;

            case 'memory':
                return true; // Always available

            default:
                return false;
        }
    }

    /**
     * Initialize basic dependencies (synchronous, safe for all environments)
     */
    private initializeBasicDependencies(): void {
        // Register environment detector (always safe)
        this.container.registerDependency('environmentDetector', getEnvironmentDetector());
    }

    /**
     * Ensure all dependencies are initialized (async, lazy loading)
     */
    private async ensureDependenciesInitialized(): Promise<void> {
        if (this.dependenciesInitialized) {
            return;
        }

        await this.registerPlatformDependencies();
        this.dependenciesInitialized = true;
    }

    /**
     * Register platform-specific dependencies (async, safe for all environments)
     */
    private async registerPlatformDependencies(): Promise<void> {
        const detector = getEnvironmentDetector();

        // Register localforage for browser environments
        if (detector.isBrowser()) {
            await this.tryRegisterBrowserDependencies();
        }

        // Register Node.js dependencies
        if (detector.isNode()) {
            await this.tryRegisterNodeDependencies();
        }

        // Register React Native dependencies
        if (detector.isReactNative()) {
            await this.tryRegisterReactNativeDependencies();
        }
    }

    /**
     * Safely register browser dependencies
     */
    private async tryRegisterBrowserDependencies(): Promise<void> {
        try {
             if (typeof window !== 'undefined') {
                // In pure browser environments, localforage should be available globally or via import
                // Try to access it from global scope first
                const globalLocalforage = (window as any).localforage;
                if (globalLocalforage) {
                    this.container.registerDependency('localforage', globalLocalforage);
                    logger.debug('Registered localforage dependency from global scope');
                } else {
                    // Fallback: dependency will be injected by the adapter itself
                    logger.debug('localforage not found in global scope, will be loaded by adapter');
                }
            }
        } catch (error) {
            logger.debug('Could not register localforage dependency:', error);
            // This is expected in some environments - the adapter will handle it
        }
    }

    /**
     * Safely register Node.js dependencies
     */
    private async tryRegisterNodeDependencies(): Promise<void> {
        try {
            // Only attempt to load Node.js modules if we're actually in Node.js
            if (typeof process !== 'undefined' && process.versions && process.versions.node) {
                const fs = require('fs').promises;
                const path = require('path');
                this.container.registerDependency('fs', fs);
                this.container.registerDependency('path', path);
                logger.debug('Registered Node.js dependencies (fs, path)');
            }
        } catch (error) {
            logger.debug('Could not register Node.js dependencies:', error);
            // This is expected in non-Node.js environments
        }
    }

    /**
     * Safely register React Native dependencies
     */
    private async tryRegisterReactNativeDependencies(): Promise<void> {
        try {
            // Check for React Native environment indicators
            if (typeof navigator !== 'undefined' &&
                navigator.userAgent &&
                navigator.userAgent.includes('ReactNative')) {
                const AsyncStorage = require('@react-native-async-storage/async-storage');
                this.container.registerDependency('asyncStorage', AsyncStorage);
                logger.debug('Registered React Native AsyncStorage dependency');
            } else if (typeof global !== 'undefined' &&
                       (typeof (global as any).HermesInternal !== 'undefined' ||
                        typeof (global as any).__METRO__ !== 'undefined')) {
                // Alternative React Native detection
                const AsyncStorage = require('@react-native-async-storage/async-storage');
                this.container.registerDependency('asyncStorage', AsyncStorage);
                logger.debug('Registered React Native AsyncStorage dependency (Hermes/Metro detected)');
            }
        } catch (error) {
            logger.debug('Could not register React Native dependencies:', error);
            // This is expected in non-React Native environments
        }
    }

    /**
     * Create a specific adapter type (for testing or explicit requirements)
     */
    async createSpecificAdapter(
        adapterType: string,
        config: StorageConfig
    ): Promise<IStorageAdapter> {
        // Ensure dependencies are initialized before creating adapters
        await this.ensureDependenciesInitialized();

        if (!this.isAdapterAvailable(adapterType)) {
            throw new Error(`Storage adapter '${adapterType}' is not available in this environment`);
        }

        return await this.container.createAdapter(adapterType, config);
    }

    /**
     * Get all available adapter types for the current platform
     */
    getAvailableAdapterTypes(): string[] {
        const types: string[] = [];

        if (this.isAdapterAvailable('browser')) {
            types.push('browser');
        }

        if (this.isAdapterAvailable('node')) {
            types.push('node');
        }

        if (this.isAdapterAvailable('react-native')) {
            types.push('react-native');
        }

        // Memory is always available
        types.push('memory');

        return types;
    }

    /**
     * Create an adapter with automatic fallback chain
     */
    async createAdapterWithFallback(
        config: StorageConfig,
        preferredTypes?: string[]
    ): Promise<IStorageAdapter> {
        const typesToTry = preferredTypes || [this.getAdapterType(), 'memory'];

        for (const adapterType of typesToTry) {
            if (this.isAdapterAvailable(adapterType)) {
                try {
                    return await this.createSpecificAdapter(adapterType, config);
                } catch (error) {
                    logger.warn(`Failed to create ${adapterType} adapter, trying next option:`, error);
                }
            }
        }

        throw new Error('No storage adapter could be created');
    }
}

/**
 * Convenience function to create a storage adapter
 */
export async function createStorageAdapter(config: StorageConfig): Promise<IStorageAdapter> {
    const factory = StorageAdapterFactory.getInstance();
    return await factory.createAdapter(config);
}

/**
 * Convenience function to create a specific storage adapter
 */
export async function createSpecificStorageAdapter(
    adapterType: string,
    config: StorageConfig
): Promise<IStorageAdapter> {
    const factory = StorageAdapterFactory.getInstance();
    return await factory.createSpecificAdapter(adapterType, config);
}

/**
 * Get the default storage factory instance
 */
export function getStorageFactory(): IStorageAdapterFactory {
    return StorageAdapterFactory.getInstance();
}
