/**
 * Comprehensive tests for BrowserStorageAdapter
 * Tests browser-specific storage functionality including localforage integration
 */

// Mock localforage before importing anything else
const mockStore = new Map();
const mockInstance = {
  ready: jest.fn().mockResolvedValue(undefined),
  getItem: jest.fn((key) => Promise.resolve(mockStore.get(key) || null)),
  setItem: jest.fn((key, value) => {
    mockStore.set(key, value);
    return Promise.resolve(value);
  }),
  removeItem: jest.fn((key) => {
    mockStore.delete(key);
    return Promise.resolve();
  }),
  clear: jest.fn(() => {
    mockStore.clear();
    return Promise.resolve();
  }),
  keys: jest.fn(() => Promise.resolve(Array.from(mockStore.keys()))),
  length: jest.fn(() => Promise.resolve(mockStore.size)),
  driver: jest.fn(() => 'asyncStorage'),
  config: jest.fn(() => ({})),
  setDriver: jest.fn().mockResolvedValue(undefined),
  supports: jest.fn(() => true)
};

jest.mock('localforage', () => ({
  createInstance: jest.fn(() => mockInstance),
  INDEXEDDB: 'asyncStorage',
  LOCALSTORAGE: 'localStorageWrapper',
  WEBSQL: 'webSQLStorage',
  supports: jest.fn(() => true)
}));

import { BrowserStorageAdapter } from '../../../adapters/BrowserStorageAdapter';
import { mockBrowserEnvironment, testBasicOperations, testTTLFunctionality, assertAdapterCapabilities, testData, wait } from '../../utils/test-helpers';

describe('BrowserStorageAdapter', () => {
  let adapter: BrowserStorageAdapter;
  let cleanupBrowserMock: () => void;

  beforeEach(async () => {
    cleanupBrowserMock = mockBrowserEnvironment();
    adapter = new BrowserStorageAdapter();
    await adapter.initialize({
      name: 'test_browser_db',
      storeName: 'test_browser_store'
    });
  });

  afterEach(async () => {
    if (adapter) {
      await adapter.dispose();
    }
    cleanupBrowserMock();
    mockStore.clear();

    // Reset all mock functions to their original implementations
    mockInstance.setItem.mockImplementation((key, value) => {
      mockStore.set(key, value);
      return Promise.resolve(value);
    });
    mockInstance.getItem.mockImplementation((key) => Promise.resolve(mockStore.get(key) || null));
    mockInstance.removeItem.mockImplementation((key) => {
      mockStore.delete(key);
      return Promise.resolve();
    });

    jest.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', async () => {
      const newAdapter = new BrowserStorageAdapter();
      await newAdapter.initialize({
        name: 'default_db',
        storeName: 'default_store'
      });

      expect(newAdapter).toBeDefined();
      const isHealthy = await newAdapter.isHealthy();
      expect(isHealthy).toBe(true);

      await newAdapter.dispose();
    });

    it('should initialize with custom configuration', async () => {
      const newAdapter = new BrowserStorageAdapter();
      await newAdapter.initialize({
        name: 'custom_db',
        storeName: 'custom_store',
        version: 2,
        description: 'Custom test database',
        platformOptions: {
          driver: ['asyncStorage']
        }
      });

      expect(newAdapter).toBeDefined();
      await newAdapter.dispose();
    });

    it('should handle initialization failure gracefully', async () => {
      const localforage = require('localforage');
      const failingMockInstance = {
        ready: jest.fn().mockRejectedValue(new Error('Initialization failed'))
      };
      localforage.createInstance.mockReturnValueOnce(failingMockInstance);

      const newAdapter = new BrowserStorageAdapter();
      await expect(newAdapter.initialize({
        name: 'failing_db',
        storeName: 'failing_store'
      })).rejects.toThrow('Failed to initialize browser storage');
    });
  });

  describe('Basic Operations', () => {
    it('should perform all basic storage operations', async () => {
      await testBasicOperations(adapter);
    });

    it('should handle different data types correctly', async () => {
      // Test simple data types
      for (const [key, value] of Object.entries(testData.simple)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }

      // Test complex data types
      for (const [key, value] of Object.entries(testData.complex)) {
        await adapter.setItem(key, value);
        const retrieved = await adapter.getItem(key);
        expect(retrieved).toEqual(value);
      }
    });

    it('should handle large datasets', async () => {
      await adapter.setItem('large_dataset', testData.large);
      const retrieved = await adapter.getItem('large_dataset');
      expect(retrieved).toEqual(testData.large);
      expect(Array.isArray(retrieved)).toBe(true);
      expect(retrieved).toHaveLength(1000);
    });
  });

  describe('TTL Functionality', () => {
    it('should handle TTL correctly', async () => {
      await testTTLFunctionality(adapter);
    });

    it('should cleanup expired items', async () => {
      // Add items with different TTLs
      await adapter.setItem('short_ttl', 'value1', 50);
      await adapter.setItem('medium_ttl', 'value2', 150);
      await adapter.setItem('no_ttl', 'value3');

      // Wait for short TTL to expire
      await wait(75);

      // Cleanup should remove expired items
      const cleanedCount = await adapter.cleanup();
      expect(cleanedCount).toBeGreaterThanOrEqual(1);

      // Check that only non-expired items remain
      expect(await adapter.getItem('short_ttl')).toBeNull();
      expect(await adapter.getItem('medium_ttl')).toBe('value2');
      expect(await adapter.getItem('no_ttl')).toBe('value3');
    });

    it('should handle TTL edge cases', async () => {
      // Zero TTL should not expire
      await adapter.setItem('zero_ttl', 'value', 0);
      await wait(100);
      expect(await adapter.getItem('zero_ttl')).toBe('value');

      // Negative TTL should not expire
      await adapter.setItem('negative_ttl', 'value', -1);
      await wait(100);
      expect(await adapter.getItem('negative_ttl')).toBe('value');
    });
  });

  describe('Storage Capabilities', () => {
    it('should report correct capabilities', () => {
      assertAdapterCapabilities(adapter, 'browser', true);
      
      const capabilities = adapter.getCapabilities();
      expect(capabilities.supportsBulkOperations).toBe(false);
      expect(capabilities.maxStorageSize).toBeGreaterThan(0);
    });

    it('should report current driver', () => {
      const driver = adapter.getCurrentDriver();
      expect(typeof driver).toBe('string');
    });
  });

  describe('Health Monitoring', () => {
    it('should report healthy status when functioning', async () => {
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(true);
    });

    it('should report unhealthy status when disposed', async () => {
      await adapter.dispose();
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle health check errors gracefully', async () => {
      // Mock storage instance to throw error
      const mockInstance = adapter['storeInstance'];
      if (mockInstance) {
        mockInstance.getItem = jest.fn().mockRejectedValue(new Error('Storage error'));
      }

      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle storage errors gracefully', async () => {
      const mockInstance = adapter['storeInstance'];

      if (mockInstance) {
        // Mock setItem to fail
        mockInstance.setItem = jest.fn().mockRejectedValue(new Error('Storage full'));

        await expect(adapter.setItem('test', 'value')).rejects.toThrow('Storage full');
      }
    });

    it('should handle invalid keys', async () => {
      const invalidKeys = ['', null, undefined];
      
      for (const key of invalidKeys) {
        try {
          await adapter.setItem(key as any, 'value');
          const result = await adapter.getItem(key as any);
          // Should either work or return null
          expect(result === null || result === 'value').toBe(true);
        } catch (error) {
          // If it throws, should be meaningful error
          expect(error).toBeInstanceOf(Error);
        }
      }
    });

    it('should handle quota exceeded scenarios', async () => {
      const mockInstance = adapter['storeInstance'];

      if (mockInstance) {
        // Mock quota exceeded error
        const quotaError = new Error('QuotaExceededError');
        quotaError.name = 'QuotaExceededError';
        mockInstance.setItem = jest.fn().mockRejectedValue(quotaError);

        await expect(adapter.setItem('large_item', 'x'.repeat(1000000))).rejects.toThrow('QuotaExceededError');
      }
    });
  });

  describe('Concurrent Operations', () => {
    it('should handle concurrent read/write operations', async () => {
      const operations = Array.from({ length: 10 }, async (_, i) => {
        await adapter.setItem(`concurrent_${i}`, `value_${i}`);
        return adapter.getItem(`concurrent_${i}`);
      });

      const results = await Promise.all(operations);
      
      results.forEach((result, i) => {
        expect(result).toBe(`value_${i}`);
      });
    });

    it('should handle concurrent cleanup operations', async () => {
      // Add items with short TTL
      const setOperations = Array.from({ length: 5 }, (_, i) =>
        adapter.setItem(`cleanup_${i}`, `value_${i}`, 50)
      );
      await Promise.all(setOperations);

      // Wait for expiration
      await wait(75);

      // Run multiple cleanup operations concurrently
      const cleanupOperations = Array.from({ length: 3 }, () => adapter.cleanup());
      const results = await Promise.all(cleanupOperations);

      // At least one cleanup should have removed items
      const totalCleaned = results.reduce((sum, count) => sum + count, 0);
      expect(totalCleaned).toBeGreaterThanOrEqual(5);
    });
  });

  describe('Browser-Specific Features', () => {
    it('should handle storage events', async () => {
      // This would test browser storage events if implemented
      // For now, just verify the adapter works in browser context
      expect(adapter.getCapabilities().platform).toBe('browser');
    });

    it('should handle page visibility changes', async () => {
      // Test that storage continues to work when page visibility changes
      await adapter.setItem('visibility_test', 'test_value');
      
      // Simulate page becoming hidden/visible
      // In a real browser, this might trigger cleanup or other operations
      const value = await adapter.getItem('visibility_test');
      expect(value).toBe('test_value');
    });

    it('should work with different localforage drivers', async () => {
      const drivers = ['asyncStorage', 'localStorageWrapper', 'webSQLStorage'];
      
      for (const driver of drivers) {
        const mockInstance = adapter['storeInstance'];
        if (mockInstance) {
          mockInstance.driver = jest.fn(() => driver);

          const currentDriver = adapter.getCurrentDriver();
          expect(currentDriver).toBe(driver);
        }
      }
    });
  });

  describe('Memory Management', () => {
    it('should dispose resources properly', async () => {
      await adapter.dispose();
      
      // After disposal, operations should fail
      await expect(adapter.getItem('test')).rejects.toThrow('Storage adapter not initialized');
      
      // Health check should return false
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });

    it('should handle multiple dispose calls', async () => {
      await adapter.dispose();
      await adapter.dispose(); // Should not throw
      
      const isHealthy = await adapter.isHealthy();
      expect(isHealthy).toBe(false);
    });
  });
});
