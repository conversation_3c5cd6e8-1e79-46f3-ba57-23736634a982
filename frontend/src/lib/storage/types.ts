/**
 * Storage abstraction types for platform-independent storage operations.
 * 
 * This module defines the interfaces and types needed to create a storage
 * abstraction layer that works across different platforms (browser, Node.js, React Native).
 */

/**
 * Configuration options for storage adapters
 */
export interface StorageConfig {
    /** Database/storage name */
    name: string;
    /** Store name within the database (for namespacing) */
    storeName: string;
    /** Optional version for schema migrations */
    version?: number;
    /** Optional description */
    description?: string;
    /** Platform-specific options */
    platformOptions?: Record<string, any>;
}

/**
 * Metadata wrapper for stored items with timestamp and TTL support
 */
export interface StoredItemWithTimestamp<T> {
    /** The actual data being stored */
    data: T;
    /** Timestamp when the item was stored (milliseconds since epoch) */
    timestamp: number;
    /** Optional TTL in milliseconds */
    ttl?: number;
}

/**
 * Storage operation result for better error handling
 */
export interface StorageResult<T> {
    /** Whether the operation was successful */
    success: boolean;
    /** The data (for get operations) */
    data?: T;
    /** Error message if operation failed */
    error?: string;
    /** Additional metadata */
    metadata?: {
        /** Whether item was expired (for get operations) */
        expired?: boolean;
        /** Original timestamp */
        timestamp?: number;
    };
}

/**
 * Storage adapter capabilities
 */
export interface StorageCapabilities {
    /** Supports TTL (time-to-live) */
    supportsTTL: boolean;
    /** Supports transactions */
    supportsTransactions: boolean;
    /** Supports bulk operations */
    supportsBulkOperations: boolean;
    /** Maximum storage size (in bytes, -1 for unlimited) */
    maxStorageSize: number;
    /** Whether storage persists across app restarts */
    persistent: boolean;
    /** Platform identifier */
    platform: 'browser' | 'node' | 'react-native' | 'memory' | 'unknown';
}

/**
 * Core storage adapter interface that all platform-specific adapters must implement.
 * 
 * This interface provides a consistent API for storage operations across different platforms.
 * All methods are async to support both synchronous and asynchronous storage backends.
 */
export interface IStorageAdapter {
    /**
     * Initialize the storage adapter
     * @param config Storage configuration
     */
    initialize(config: StorageConfig): Promise<void>;

    /**
     * Get an item from storage
     * @param key The key to retrieve
     * @returns Promise resolving to the stored value or null if not found/expired
     */
    getItem<T>(key: string): Promise<T | null>;

    /**
     * Set an item in storage
     * @param key The key to store under
     * @param value The value to store
     * @param ttl Optional TTL in milliseconds
     */
    setItem<T>(key: string, value: T, ttl?: number): Promise<void>;

    /**
     * Remove an item from storage
     * @param key The key to remove
     */
    removeItem(key: string): Promise<void>;

    /**
     * Clear all items from this storage instance
     */
    clear(): Promise<void>;

    /**
     * Get all keys in storage
     * @returns Promise resolving to array of all keys
     */
    keys(): Promise<string[]>;

    /**
     * Get the number of items in storage
     * @returns Promise resolving to the count of items
     */
    length(): Promise<number>;

    /**
     * Check if storage is available and working
     * @returns Promise resolving to true if storage is healthy
     */
    isHealthy(): Promise<boolean>;

    /**
     * Get storage adapter capabilities
     * @returns The capabilities of this storage adapter
     */
    getCapabilities(): StorageCapabilities;

    /**
     * Get storage usage information
     * @returns Promise resolving to usage stats
     */
    getUsage(): Promise<{
        used: number;
        available: number;
        total: number;
    }>;

    /**
     * Cleanup expired items (if TTL is supported)
     * @returns Promise resolving to number of items cleaned up
     */
    cleanup(): Promise<number>;

    /**
     * Close/dispose the storage adapter
     */
    dispose(): Promise<void>;
}

/**
 * Factory interface for creating storage adapters
 */
export interface IStorageAdapterFactory {
    /**
     * Create a storage adapter for the current platform
     * @param config Storage configuration
     * @returns Promise resolving to a storage adapter instance
     */
    createAdapter(config: StorageConfig): Promise<IStorageAdapter>;

    /**
     * Get the best available adapter for the current platform
     * @returns The adapter type that will be created
     */
    getAdapterType(): string;

    /**
     * Check if a specific adapter type is available
     * @param adapterType The adapter type to check
     * @returns Whether the adapter is available
     */
    isAdapterAvailable(adapterType: string): boolean;
}

/**
 * Storage manager interface for high-level storage operations
 */
export interface IStorageManager {
    /**
     * Get a storage instance for a specific store
     * @param storeName The name of the store
     * @param ttl Optional default TTL for items in this store
     * @returns Promise resolving to a storage adapter instance
     */
    getStorage(storeName: string, ttl?: number): Promise<IStorageAdapter>;

    /**
     * Initialize the storage manager
     * @param config Global storage configuration
     */
    initialize(config: Partial<StorageConfig>): Promise<void>;

    /**
     * Cleanup all storage instances
     */
    cleanup(): Promise<void>;

    /**
     * Get storage health status
     */
    getHealthStatus(): Promise<{
        healthy: boolean;
        adapters: Array<{
            storeName: string;
            healthy: boolean;
            error?: string;
        }>;
    }>;
}

/**
 * Platform detection result
 */
export interface PlatformInfo {
    /** Platform type */
    platform: 'browser' | 'node' | 'react-native' | 'unknown';
    /** Whether we're in a server environment */
    isServer: boolean;
    /** Whether we're in a browser environment */
    isBrowser: boolean;
    /** Whether we're in React Native */
    isReactNative: boolean;
    /** Additional platform details */
    details: {
        userAgent?: string;
        nodeVersion?: string;
        reactNativeVersion?: string;
        environment?: string;
        platform?: string;
        isSSR?: boolean;
    };
}
