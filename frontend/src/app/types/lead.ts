import { z } from 'zod';

// Lead Types and Sources (based on API)
export const LEAD_TYPES = ["CALLBACK_REQUESTED"] as const;
export const LEAD_SOURCES = ["WHATSAPP"] as const;

// Simplified Lead Status Flow
export const LEAD_STATUSES = [
    "PENDING",          // Pending lead, not contacted
    "CONTACTED",        // Initial contact made  
    "INTERESTED",       // Shows genuine interest
    "CONVERTED",        // Successfully converted to order
    "LOST"              // Didn't convert (reason in metadata)
] as const;

export type LeadType = typeof LEAD_TYPES[number];
export type LeadSource = typeof LEAD_SOURCES[number];
export type LeadStatus = typeof LEAD_STATUSES[number];

// Metadata Structure for Leads (optional field)
export interface LeadNote {
    id: string;
    content: string;
    createdAt: number;
    createdBy: number;
    createByName?: string;
}

export interface LeadTimelineEntry {
    id: string;
    action: string;
    details: string;
    timestamp: number;
    userId: number;
}

export interface LeadConversionData {
    orderId?: string;
    orderValue?: number;
    convertedAt?: number;
}

export interface LeadMetadata {
    notes?: LeadNote[];
    timeline?: LeadTimelineEntry[];
    customFields?: Record<string, any>;
    lostReason?: string; // When status = "LOST"
    nextFollowUp?: number; // timestamp
    leadScore?: number; // 1-10 scoring
    conversionData?: LeadConversionData;
}

// Main Lead Interface (based on exact API response)
export interface Lead {
    id: number;
    name: string;
    type: LeadType;
    status: LeadStatus;
    row_updated_at: number; // timestamp in milliseconds
    row_created_at: number; // timestamp in milliseconds
    leadSource: LeadSource;
    endCustomerMobile: string; // "919791945220" format
    conversionTime: number | null;
    metadata?: LeadMetadata; // Optional field - may not be present in API response
}

// API Request/Response Interfaces
export interface GetLeadsRequest {
    limit: number;
    pageNumber: number;
    leadSource?: string;
    name?: string;
    type?: string;
    status?: string;
    endCustomerMobile?: string;
}

export interface GetLeadsResponse {
    appliedLimit: number;
    currentPageNumber: number;
    totalNumberOfPages: number;
    totalNumberOfRows: number;
    leads: Lead[];
}

export interface GetLeadByIdRequest {
    id: number;
}

export interface GetLeadByIdResponse {
    infinityLead: Lead;
}

// Filter Interface for Lead Filtering
export interface LeadFilters {
    search: string; // For name and mobile search
    leadSource: string;
    type: string;
    status: string;
    dateFrom: string; // YYYY-MM-DD format
    dateTo: string; // YYYY-MM-DD format
}

// Zod Schemas for Validation
export const leadMetadataSchema = z.object({
    notes: z.array(z.object({
        id: z.string(),
        content: z.string(),
        createdAt: z.number(),
        createdBy: z.number()
    })).optional(),
    timeline: z.array(z.object({
        id: z.string(),
        action: z.string(),
        details: z.string(),
        timestamp: z.number(),
        userId: z.number()
    })).optional(),
    customFields: z.record(z.any()).optional(),
    lostReason: z.string().optional(),
    nextFollowUp: z.number().optional(),
    leadScore: z.number().min(1).max(10).optional(),
    conversionData: z.object({
        orderId: z.string().optional(),
        orderValue: z.number().optional(),
        convertedAt: z.number().optional()
    }).optional()
}).optional();

export const leadSchema = z.object({
    id: z.number(),
    name: z.string(),
    type: z.enum(LEAD_TYPES),
    status: z.enum(LEAD_STATUSES),
    row_updated_at: z.number(),
    row_created_at: z.number(),
    leadSource: z.enum(LEAD_SOURCES),
    endCustomerMobile: z.string(),
    conversionTime: z.number().nullable(),
    metadata: leadMetadataSchema
});

export const getLeadsRequestSchema = z.object({
    limit: z.number().positive(),
    pageNumber: z.number().positive(),
    leadSource: z.string().optional(),
    name: z.string().optional(),
    type: z.string().optional(),
    status: z.string().optional(),
    endCustomerMobile: z.string().optional()
}); 