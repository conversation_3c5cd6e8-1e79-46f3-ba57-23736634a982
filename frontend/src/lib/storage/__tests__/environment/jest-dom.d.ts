/**
 * Type declarations for jest-dom matchers
 */

declare namespace jest {
  interface Matchers<R> {
    toBeInTheDocument(): R;
    toHaveTextContent(text: string | RegExp): R;
    toHaveAttribute(attr: string, value?: string): R;
    toHaveClass(className: string): R;
    toBeVisible(): R;
    toBeDisabled(): R;
    toBeEnabled(): R;
    toHaveValue(value: string | number): R;
    toHaveDisplayValue(value: string | RegExp | Array<string | RegExp>): R;
    toBeChecked(): R;
    toHaveFocus(): R;
    toBeInvalid(): R;
    toBeValid(): R;
    toBeRequired(): R;
    toHaveDescription(text?: string | RegExp): R;
    toHaveAccessibleDescription(text?: string | RegExp): R;
    toHaveAccessibleName(text?: string | RegExp): R;
    toHaveErrorMessage(text?: string | RegExp): R;
  }
}
