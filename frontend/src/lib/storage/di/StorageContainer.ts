/**
 * Dependency injection container for storage adapters.
 * 
 * This module provides a dependency injection system that allows for
 * easy mocking and testing of storage adapters and their dependencies.
 */

import type { IStorageAdapter, StorageConfig } from '../types';
import type { IEnvironmentDetector } from '../environment/EnvironmentDetector';

/**
 * Factory function type for creating storage adapters
 */
export type StorageAdapterFactory<T extends IStorageAdapter = IStorageAdapter> = (
    dependencies: StorageDependencies
) => T;

/**
 * Dependencies that can be injected into storage adapters
 */
export interface StorageDependencies {
    environmentDetector: IEnvironmentDetector;
    localforage?: any;
    fs?: any;
    path?: any;
    asyncStorage?: any;
}

/**
 * Storage adapter registration
 */
interface StorageAdapterRegistration {
    factory: StorageAdapterFactory;
    dependencies: string[];
}

/**
 * Dependency injection container for storage system
 */
export class StorageContainer {
    private static instance: StorageContainer | null = null;
    private adapters = new Map<string, StorageAdapterRegistration>();
    private dependencies = new Map<string, any>();
    private singletons = new Map<string, IStorageAdapter>();

    private constructor() {
        this.registerDefaultDependencies();
        this.registerDefaultAdapters();
    }

    /**
     * Get singleton instance
     */
    static getInstance(): StorageContainer {
        if (!StorageContainer.instance) {
            StorageContainer.instance = new StorageContainer();
        }
        return StorageContainer.instance;
    }

    /**
     * Reset the container (useful for testing)
     */
    static reset(): void {
        StorageContainer.instance = null;
    }

    /**
     * Register a dependency
     */
    registerDependency<T>(name: string, dependency: T): void {
        this.dependencies.set(name, dependency);
    }

    /**
     * Get a dependency
     */
    getDependency<T>(name: string): T {
        if (!this.dependencies.has(name)) {
            throw new Error(`Dependency '${name}' not registered`);
        }
        return this.dependencies.get(name) as T;
    }

    /**
     * Register a storage adapter factory
     */
    registerAdapter(
        name: string, 
        factory: StorageAdapterFactory,
        dependencies: string[] = []
    ): void {
        this.adapters.set(name, { factory, dependencies });
    }

    /**
     * Create a storage adapter instance
     */
    async createAdapter(name: string, config: StorageConfig): Promise<IStorageAdapter> {
        const registration = this.adapters.get(name);
        if (!registration) {
            throw new Error(`Storage adapter '${name}' not registered`);
        }

        // Resolve dependencies
        const resolvedDependencies: StorageDependencies = {
            environmentDetector: this.dependencies.has('environmentDetector')
                ? this.getDependency('environmentDetector')
                : require('../environment/EnvironmentDetector').getEnvironmentDetector()
        };

        for (const depName of registration.dependencies) {
            if (this.dependencies.has(depName) && depName in resolvedDependencies) {
                const key = depName as keyof StorageDependencies;
                (resolvedDependencies as any)[key] = this.getDependency(depName);
            }
        }

        // Create adapter instance
        const adapter = registration.factory(resolvedDependencies);
        await adapter.initialize(config);
        return adapter;
    }

    /**
     * Create a singleton adapter instance
     */
    async createSingletonAdapter(name: string, config: StorageConfig): Promise<IStorageAdapter> {
        const key = `${name}:${config.storeName}`;
        
        if (this.singletons.has(key)) {
            return this.singletons.get(key)!;
        }

        const adapter = await this.createAdapter(name, config);
        this.singletons.set(key, adapter);
        return adapter;
    }

    /**
     * Check if an adapter is registered
     */
    hasAdapter(name: string): boolean {
        return this.adapters.has(name);
    }

    /**
     * Get all registered adapter names
     */
    getRegisteredAdapters(): string[] {
        return Array.from(this.adapters.keys());
    }

    /**
     * Clear all singletons (useful for cleanup)
     */
    async clearSingletons(): Promise<void> {
        for (const adapter of this.singletons.values()) {
            try {
                await adapter.dispose();
            } catch (error) {
                // Ignore disposal errors
            }
        }
        this.singletons.clear();
    }

    /**
     * Register default dependencies
     */
    private registerDefaultDependencies(): void {
        // Environment detector will be set by the factory
        // Other dependencies will be registered as needed
    }

    /**
     * Register default adapters
     */
    private registerDefaultAdapters(): void {
        // Memory adapter (always available)
        this.registerAdapter('memory', (deps) => {
            const { MemoryStorageAdapter } = require('../adapters/MemoryStorageAdapter');
            return new MemoryStorageAdapter();
        });

        // Browser adapter
        this.registerAdapter('browser', (deps) => {
            const { BrowserStorageAdapter } = require('../adapters/BrowserStorageAdapter');
            return new BrowserStorageAdapter(deps.localforage);
        }, ['localforage']);

        // Node adapter
        this.registerAdapter('node', (deps) => {
            const { NodeStorageAdapter } = require('../adapters/NodeStorageAdapter');
            return new NodeStorageAdapter(deps.fs, deps.path);
        }, ['fs', 'path']);

        // React Native adapter
        this.registerAdapter('react-native', (deps) => {
            const { ReactNativeStorageAdapter } = require('../adapters/ReactNativeStorageAdapter');
            return new ReactNativeStorageAdapter(deps.asyncStorage);
        }, ['asyncStorage']);
    }
}

/**
 * Convenience function to get the storage container
 */
export function getStorageContainer(): StorageContainer {
    return StorageContainer.getInstance();
}

/**
 * Convenience function to register a mock dependency for testing
 */
export function registerMockDependency<T>(name: string, mock: T): void {
    const container = getStorageContainer();
    container.registerDependency(name, mock);
}

/**
 * Convenience function to register a mock adapter for testing
 */
export function registerMockAdapter(
    name: string, 
    factory: StorageAdapterFactory,
    dependencies: string[] = []
): void {
    const container = getStorageContainer();
    container.registerAdapter(name, factory, dependencies);
}

/**
 * Reset the container for testing
 */
export function resetStorageContainer(): void {
    StorageContainer.reset();
}
