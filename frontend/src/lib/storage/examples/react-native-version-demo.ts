/**
 * React Native Version Detection Demo
 * 
 * This file demonstrates the improved React Native version detection capabilities
 * of the platform detection utilities. It shows how the system can detect
 * React Native versions from multiple sources and provide meaningful fallbacks.
 */

import { 
    detectPlatform, 
    isReactNative, 
    getReactNativeInfo 
} from '../platformDetection';

/**
 * Demo function to showcase React Native version detection
 */
export function demonstrateReactNativeVersionDetection() {
    console.log('=== React Native Version Detection Demo ===\n');

    // Basic platform detection
    const platform = detectPlatform();
    console.log('Platform Detection Result:');
    console.log(JSON.stringify(platform, null, 2));
    console.log('\n');

    // Check if we're in React Native environment
    const isRN = isReactNative();
    console.log(`Is React Native Environment: ${isRN}`);
    console.log('\n');

    // Get detailed React Native information
    const rnInfo = getReactNativeInfo();
    if (rnInfo) {
        console.log('Detailed React Native Information:');
        console.log(`Version: ${rnInfo.version || 'Not detected'}`);
        console.log(`Engine: ${rnInfo.engine || 'Unknown'}`);
        console.log(`Bundler: ${rnInfo.bundler || 'Unknown'}`);
        console.log(`Development Mode: ${rnInfo.isDevelopment ?? 'Unknown'}`);
        console.log(`Capabilities: ${rnInfo.capabilities.length > 0 ? rnInfo.capabilities.join(', ') : 'None detected'}`);
    } else {
        console.log('Not running in React Native environment');
    }
    console.log('\n');

    // Show version detection strategies
    console.log('Version Detection Strategies:');
    console.log('1. Platform Module Constants - Checks React Native Platform.constants.reactNativeVersion');
    console.log('2. Package.json Version - Attempts to read react-native/package.json');
    console.log('3. Global Constants - Checks for global version variables and Hermes engine info');
    console.log('4. User Agent Parsing - Extracts version from navigator.userAgent');
    console.log('5. Environment Detection - Detects development/production mode and available APIs');
    console.log('\n');

    return {
        platform,
        isReactNative: isRN,
        reactNativeInfo: rnInfo
    };
}

/**
 * Mock React Native environment for testing purposes
 */
export function mockReactNativeEnvironment(version: string = '0.72.0') {
    // Store original values
    const originalNavigator = (global as any).navigator;
    const originalGlobal = { ...global };

    // Mock React Native environment
    (global as any).navigator = {
        userAgent: `ReactNative/${version}`,
        product: 'ReactNative'
    };

    // Mock Hermes engine
    (global as any).HermesInternal = {
        getRuntimeProperties: () => ({
            'OSS Release Version': '0.12.0'
        })
    };

    // Mock development environment
    (global as any).__DEV__ = true;
    (global as any).__METRO__ = true;

    console.log(`Mocked React Native ${version} environment`);

    // Return cleanup function
    return () => {
        (global as any).navigator = originalNavigator;
        (global as any).HermesInternal = undefined;
        (global as any).__DEV__ = undefined;
        (global as any).__METRO__ = undefined;
        console.log('Cleaned up mocked React Native environment');
    };
}

/**
 * Run a comprehensive demo showing different React Native scenarios
 */
export function runComprehensiveDemo() {
    console.log('=== Comprehensive React Native Version Detection Demo ===\n');

    // Test 1: Current environment
    console.log('Test 1: Current Environment');
    demonstrateReactNativeVersionDetection();

    // Test 2: Mock React Native 0.72.0
    console.log('Test 2: Mocked React Native 0.72.0');
    const cleanup1 = mockReactNativeEnvironment('0.72.0');
    demonstrateReactNativeVersionDetection();
    cleanup1();

    // Test 3: Mock React Native 0.71.8
    console.log('Test 3: Mocked React Native 0.71.8');
    const cleanup2 = mockReactNativeEnvironment('0.71.8');
    demonstrateReactNativeVersionDetection();
    cleanup2();

    // Test 4: Mock React Native with Hermes only
    console.log('Test 4: Mocked React Native with Hermes Engine Only');
    const cleanup3 = (() => {
        (global as any).navigator = { product: 'ReactNative' };
        (global as any).HermesInternal = {
            getRuntimeProperties: () => ({
                'OSS Release Version': '0.12.0'
            })
        };
        return () => {
            (global as any).navigator = undefined;
            (global as any).HermesInternal = undefined;
        };
    })();
    demonstrateReactNativeVersionDetection();
    cleanup3();

    console.log('=== Demo Complete ===');
}

// Example usage patterns
export const examples = {
    // Basic usage
    basic: () => {
        const platform = detectPlatform();
        if (platform.isReactNative && platform.details.reactNativeVersion) {
            console.log(`Running on React Native ${platform.details.reactNativeVersion}`);
        }
    },

    // Detailed analysis
    detailed: () => {
        const rnInfo = getReactNativeInfo();
        if (rnInfo) {
            console.log('React Native Environment Analysis:');
            console.log(`- Version: ${rnInfo.version}`);
            console.log(`- JavaScript Engine: ${rnInfo.engine}`);
            console.log(`- Bundler: ${rnInfo.bundler}`);
            console.log(`- Development Mode: ${rnInfo.isDevelopment}`);
            console.log(`- Available Capabilities: ${rnInfo.capabilities.join(', ')}`);
        }
    },

    // Conditional feature detection
    featureDetection: () => {
        const rnInfo = getReactNativeInfo();
        if (rnInfo) {
            // Check for specific capabilities
            const hasAsyncStorage = rnInfo.capabilities.includes('async-storage');
            const hasGeolocation = rnInfo.capabilities.includes('geolocation');
            const isHermes = rnInfo.engine === 'hermes';
            
            console.log(`AsyncStorage available: ${hasAsyncStorage}`);
            console.log(`Geolocation available: ${hasGeolocation}`);
            console.log(`Using Hermes engine: ${isHermes}`);
        }
    }
};
