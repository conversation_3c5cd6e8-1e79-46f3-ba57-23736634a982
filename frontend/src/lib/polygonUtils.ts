import pointInPolygonHao from 'point-in-polygon-hao'

/**
 * Geographic coordinate representing a latitude/longitude point
 */
export interface LatLng {
    lat: number;
    lng: number;
}

/**
 * Result of point-in-polygon test
 * - true: point is inside the polygon
 * - false: point is outside the polygon
 * - 0: point is exactly on the polygon boundary
 */
export type PointInPolygonResult = boolean | 0;

/**
 * Determines if a geographic point (lat/lng) falls inside a polygon.
 *
 * This function is optimized for rural markets and handles:
 * - Floating-point precision issues common on mobile devices
 * - Edge cases like points exactly on polygon boundaries
 * - Self-intersecting polygons
 * - Automatic polygon closing if not already closed
 *
 * @param point - The point to test as {lat, lng}
 * @param polygon - Array of polygon vertices as {lat, lng} points
 * @returns true if inside, false if outside, 0 if exactly on boundary
 *
 * @example
 * ```typescript
 * const userLocation = { lat: 28.6139, lng: 77.2090 }; // New Delhi
 *
 * const delhiBoundary = [
 *   { lat: 28.7041, lng: 77.1025 }, // Northwest
 *   { lat: 28.7041, lng: 77.2500 }, // Northeast
 *   { lat: 28.4595, lng: 77.2500 }, // Southeast
 *   { lat: 28.4595, lng: 77.1025 }, // Southwest
 *   // No need to close - function handles it automatically
 * ];
 *
 * const canDeliver = isPointInPolygon(userLocation, delhiBoundary);
 * // Returns: true
 * ```
 *
 * @example
 * ```typescript
 * // Service area around a rural market
 * const marketLocation = { lat: 25.5941, lng: 85.1376 }; // Patna
 * const serviceArea = [
 *   { lat: 25.6041, lng: 85.1276 }, // 1km radius approx
 *   { lat: 25.6041, lng: 85.1476 },
 *   { lat: 25.5841, lng: 85.1476 },
 *   { lat: 25.5841, lng: 85.1276 }
 * ];
 *
 * const canServe = isPointInPolygon(marketLocation, serviceArea);
 * ```
 */
export function isPointInPolygon(
    point: LatLng,
    polygon: LatLng[]
): PointInPolygonResult {
    // Validate inputs
    if (!polygon || polygon.length < 3) {
        throw new Error('Invalid polygon: must have at least 3 points to form a polygon');
    }

    // Validate coordinate ranges for geographic data
    if (point.lat < -90 || point.lat > 90) {
        throw new Error(`Invalid latitude: ${point.lat}. Must be between -90 and 90.`);
    }
    if (point.lng < -180 || point.lng > 180) {
        throw new Error(`Invalid longitude: ${point.lng}. Must be between -180 and 180.`);
    }

    // Validate polygon points
    for (let i = 0; i < polygon.length; i++) {
        const p = polygon[i];
        if (p.lat < -90 || p.lat > 90) {
            throw new Error(`Invalid polygon latitude at index ${i}: ${p.lat}. Must be between -90 and 90.`);
        }
        if (p.lng < -180 || p.lng > 180) {
            throw new Error(`Invalid polygon longitude at index ${i}: ${p.lng}. Must be between -180 and 180.`);
        }
    }

    // Convert LatLng to [lng, lat] format expected by point-in-polygon-hao
    const testPoint: [number, number] = [point.lng, point.lat];

    // Convert polygon LatLng[] to [lng, lat][] format and ensure it's closed
    const polygonCoords: [number, number][] = polygon.map(p => [p.lng, p.lat]);

    // Auto-close polygon if not already closed
    const first = polygonCoords[0];
    const last = polygonCoords[polygonCoords.length - 1];
    if (first[0] !== last[0] || first[1] !== last[1]) {
        polygonCoords.push([first[0], first[1]]); // Close the polygon
    }

    // point-in-polygon-hao expects: [exterior] (array of polygon rings)
    const polygonForTest: [number, number][][] = [polygonCoords];

    try {
        return pointInPolygonHao(testPoint, polygonForTest);
    } catch (error) {
        throw new Error(`Point-in-polygon calculation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * Helper function to create a simple rectangular polygon from bounds
 * Useful for creating quick geographic bounding boxes
 *
 * @param bounds - Geographic bounds {north, south, east, west}
 * @returns Array of LatLng points representing the rectangular area
 *
 * @example
 * ```typescript
 * const delhiBounds = {
 *   north: 28.88,
 *   south: 28.40,
 *   east: 77.35,
 *   west: 76.84
 * };
 *
 * const delhiRect = createRectangularPolygon(delhiBounds);
 * const isInDelhi = isPointInPolygon({ lat: 28.6139, lng: 77.2090 }, delhiRect);
 * ```
 */
export function createRectangularPolygon(bounds: {
    north: number;
    south: number;
    east: number;
    west: number;
}): LatLng[] {
    const { north, south, east, west } = bounds;

    // Validate bounds
    if (north <= south) {
        throw new Error('Invalid bounds: north must be greater than south');
    }
    if (east <= west) {
        throw new Error('Invalid bounds: east must be greater than west');
    }

    return [
        { lat: north, lng: west },  // top-left
        { lat: north, lng: east },  // top-right
        { lat: south, lng: east },  // bottom-right
        { lat: south, lng: west },  // bottom-left
        // No need to close - isPointInPolygon handles it automatically
    ];
}

/**
 * Helper function to create a circular polygon approximation around a center point
 * Useful for creating service areas around a location
 *
 * @param center - Center point of the circle
 * @param radiusKm - Radius in kilometers
 * @param segments - Number of segments to approximate the circle (default: 16)
 * @returns Array of LatLng points approximating a circle
 *
 * @example
 * ```typescript
 * const marketCenter = { lat: 28.6139, lng: 77.2090 };
 * const serviceArea = createCircularPolygon(marketCenter, 5, 12); // 5km radius, 12 segments
 *
 * const customerLocation = { lat: 28.6200, lng: 77.2150 };
 * const canDeliver = isPointInPolygon(customerLocation, serviceArea);
 * ```
 */
export function createCircularPolygon(
    center: LatLng,
    radiusKm: number,
    segments: number = 16
): LatLng[] {
    if (radiusKm <= 0) {
        throw new Error('Radius must be greater than 0');
    }
    if (segments < 3) {
        throw new Error('Segments must be at least 3 to form a polygon');
    }

    const points: LatLng[] = [];

    // Earth radius in kilometers
    const earthRadiusKm = 6371;

    // Convert radius to angular distance
    const angularDistance = radiusKm / earthRadiusKm;

    // Convert center coordinates to radians
    const centerLatRad = (center.lat * Math.PI) / 180;
    const centerLngRad = (center.lng * Math.PI) / 180;

    for (let i = 0; i < segments; i++) {
        // Calculate bearing for this segment
        const bearing = (2 * Math.PI * i) / segments;

        // Calculate point on circle using spherical trigonometry
        const pointLatRad = Math.asin(
            Math.sin(centerLatRad) * Math.cos(angularDistance) +
            Math.cos(centerLatRad) * Math.sin(angularDistance) * Math.cos(bearing)
        );

        const pointLngRad = centerLngRad + Math.atan2(
            Math.sin(bearing) * Math.sin(angularDistance) * Math.cos(centerLatRad),
            Math.cos(angularDistance) - Math.sin(centerLatRad) * Math.sin(pointLatRad)
        );

        // Convert back to degrees
        const pointLat = (pointLatRad * 180) / Math.PI;
        const pointLng = (pointLngRad * 180) / Math.PI;

        points.push({ lat: pointLat, lng: pointLng });
    }

    return points;
}

export const getClosestFacility = (lat: number, lng: number): string | null => {
    for (const villageIndex in villageBoundaries) {
        const polygon = villageBoundaries[villageIndex as keyof typeof villageBoundaries] as LatLng[];
        if (isPointInPolygon({ lat, lng }, polygon)) {
            return villageToFacilityMap[villageIndex as keyof typeof villageToFacilityMap];
        }
    }
    return null;
}

export const villageBoundaries = {
    "Sridevimangalam": [
        { lat: 11.1166542, lng: 78.8019331 },
        { lat: 11.1152646, lng: 78.801461 },
        { lat: 11.1140855, lng: 78.799959 },
        { lat: 11.1122326, lng: 78.7995298 },
        { lat: 11.1106745, lng: 78.8000019 },
        { lat: 11.1097059, lng: 78.8002165 },
        { lat: 11.1101692, lng: 78.8007744 },
        { lat: 11.1110535, lng: 78.8005598 },
        { lat: 11.1108429, lng: 78.8018902 },
        { lat: 11.110864, lng: 78.8027914 },
        { lat: 11.1100428, lng: 78.8034137 },
        { lat: 11.1096428, lng: 78.803478 },
        { lat: 11.1093901, lng: 78.8051946 },
        { lat: 11.1097059, lng: 78.8059457 },
        { lat: 11.1105061, lng: 78.805774 },
        { lat: 11.1107166, lng: 78.8049372 },
        { lat: 11.1111377, lng: 78.8039072 },
        { lat: 11.112022, lng: 78.8029845 },
        { lat: 11.1129274, lng: 78.8029201 },
        { lat: 11.1137696, lng: 78.8033922 },
        { lat: 11.1147592, lng: 78.803993 },
        { lat: 11.1157067, lng: 78.8036068 },
        { lat: 11.116591, lng: 78.8027914 },
        { lat: 11.1166542, lng: 78.8019331 },
    ],
    "Padalur 3": [
        { lat: 11.0907597, lng: 78.8295581 },
        { lat: 11.0899175, lng: 78.8296117 },
        { lat: 11.0892015, lng: 78.8300838 },
        { lat: 11.0886119, lng: 78.8303198 },
        { lat: 11.0886435, lng: 78.8311996 },
        { lat: 11.088654, lng: 78.8321437 },
        { lat: 11.0892857, lng: 78.8320686 },
        { lat: 11.0895174, lng: 78.8325192 },
        { lat: 11.0897279, lng: 78.8342251 },
        { lat: 11.090307, lng: 78.8345363 },
        { lat: 11.090332, lng: 78.8331893 },
        { lat: 11.0906229, lng: 78.8321759 },
        { lat: 11.0907597, lng: 78.8295581 },
    ],
    "Padalur 1": [
        { lat: 11.0950092, lng: 78.8314246 },
        { lat: 11.0949249, lng: 78.8309311 },
        { lat: 11.0952829, lng: 78.8300406 },
        { lat: 11.0952303, lng: 78.8292896 },
        { lat: 11.0954619, lng: 78.8288175 },
        { lat: 11.0950934, lng: 78.8287746 },
        { lat: 11.0945143, lng: 78.8288068 },
        { lat: 11.093651, lng: 78.8290321 },
        { lat: 11.0933457, lng: 78.8292467 },
        { lat: 11.0926719, lng: 78.8291931 },
        { lat: 11.0926087, lng: 78.8294291 },
        { lat: 11.0927351, lng: 78.8299441 },
        { lat: 11.0931, lng: 78.83 },
        { lat: 11.0930625, lng: 78.8305625 },
        { lat: 11.0928824, lng: 78.8310277 },
        { lat: 11.0930298, lng: 78.8315749 },
        { lat: 11.0932088, lng: 78.8319718 },
        { lat: 11.0933668, lng: 78.832004 },
        { lat: 11.093651, lng: 78.8314783 },
        { lat: 11.0941459, lng: 78.8315641 },
        { lat: 11.094209, lng: 78.83224 },
        { lat: 11.0945459, lng: 78.8322508 },
        { lat: 11.0945775, lng: 78.8316178 },
        { lat: 11.0950092, lng: 78.8314246 },
    ],
    "Padalur 7": [
        { lat: 11.0980693, lng: 78.8265324 },
        { lat: 11.1001117, lng: 78.8271493 },
        { lat: 11.1005645, lng: 78.8271064 },
        { lat: 11.1021753, lng: 78.826806 },
        { lat: 11.1021331, lng: 78.8262266 },
        { lat: 11.1019857, lng: 78.825658 },
        { lat: 11.101133, lng: 78.8256366 },
        { lat: 11.1005118, lng: 78.8259262 },
        { lat: 11.1005645, lng: 78.8267845 },
        { lat: 11.0993537, lng: 78.8267202 },
        { lat: 11.0979219, lng: 78.8263554 },
        { lat: 11.0979114, lng: 78.825304 },
        { lat: 11.0982798, lng: 78.8247246 },
        { lat: 11.0991116, lng: 78.8241667 },
        { lat: 11.0995853, lng: 78.8236732 },
        { lat: 11.0999328, lng: 78.8231367 },
        { lat: 11.1006382, lng: 78.8229114 },
        { lat: 11.1006276, lng: 78.8223857 },
        { lat: 11.1001223, lng: 78.8224715 },
        { lat: 11.1000696, lng: 78.8219566 },
        { lat: 11.1000696, lng: 78.8211948 },
        { lat: 11.1001749, lng: 78.8204331 },
        { lat: 11.1003434, lng: 78.8200468 },
        { lat: 11.1004276, lng: 78.8196606 },
        { lat: 11.0999117, lng: 78.8189525 },
        { lat: 11.0983746, lng: 78.8187165 },
        { lat: 11.0979114, lng: 78.8191027 },
        { lat: 11.098143, lng: 78.8202185 },
        { lat: 11.0994485, lng: 78.8203687 },
        { lat: 11.0993853, lng: 78.8211197 },
        { lat: 11.0993432, lng: 78.8223857 },
        { lat: 11.0991326, lng: 78.8232226 },
        { lat: 11.0979535, lng: 78.8243384 },
        { lat: 11.0971744, lng: 78.8247031 },
        { lat: 11.0964269, lng: 78.8242847 },
        { lat: 11.0964269, lng: 78.8237054 },
        { lat: 11.0979219, lng: 78.8236088 },
        { lat: 11.0978692, lng: 78.8229007 },
        { lat: 11.0962268, lng: 78.8229865 },
        { lat: 11.0958268, lng: 78.8238019 },
        { lat: 11.0941844, lng: 78.8242203 },
        { lat: 11.0941633, lng: 78.825261 },
        { lat: 11.093879, lng: 78.8254166 },
        { lat: 11.0940633, lng: 78.8255293 },
        { lat: 11.0980693, lng: 78.8265324 },
    ],
    "Padalur 4": [
        { lat: 11.0988363, lng: 78.8272223 },
        { lat: 11.0988234, lng: 78.8271269 },
        { lat: 11.098118, lng: 78.8269659 },
        { lat: 11.0951644, lng: 78.8261399 },
        { lat: 11.0929668, lng: 78.8256881 },
        { lat: 11.0933721, lng: 78.8274369 },
        { lat: 11.0937143, lng: 78.8289443 },
        { lat: 11.0944512, lng: 78.828778 },
        { lat: 11.0948619, lng: 78.8287405 },
        { lat: 11.0951461, lng: 78.8287244 },
        { lat: 11.0953146, lng: 78.8287458 },
        { lat: 11.0956567, lng: 78.8288209 },
        { lat: 11.0959726, lng: 78.8287244 },
        { lat: 11.0960884, lng: 78.8286814 },
        { lat: 11.0965516, lng: 78.8284883 },
        { lat: 11.0967201, lng: 78.8286707 },
        { lat: 11.0970886, lng: 78.8287995 },
        { lat: 11.0973834, lng: 78.8289497 },
        { lat: 11.0973728, lng: 78.8276408 },
        { lat: 11.0978887, lng: 78.8275227 },
        { lat: 11.097994, lng: 78.8293359 },
        { lat: 11.0986046, lng: 78.8292823 },
        { lat: 11.0986362, lng: 78.8281987 },
        { lat: 11.0983836, lng: 78.8274584 },
        { lat: 11.0988363, lng: 78.8272223 },
    ],
    "Padalur 5": [
        {
            "lat": 11.0935905,
            "lng": 78.8289015
        },
        {
            "lat": 11.0933905,
            "lng": 78.8279038
        },
        {
            "lat": 11.0930062,
            "lng": 78.8263964
        },
        {
            "lat": 11.092843,
            "lng": 78.8256614
        },
        {
            "lat": 11.0915059,
            "lng": 78.825479
        },
        {
            "lat": 11.0860416,
            "lng": 78.8252215
        },
        {
            "lat": 11.0860942,
            "lng": 78.826273
        },
        {
            "lat": 11.0865785,
            "lng": 78.8264232
        },
        {
            "lat": 11.0870839,
            "lng": 78.8261871
        },
        {
            "lat": 11.0869997,
            "lng": 78.8254576
        },
        {
            "lat": 11.0877893,
            "lng": 78.825522
        },
        {
            "lat": 11.0878946,
            "lng": 78.8258545
        },
        {
            "lat": 11.0885474,
            "lng": 78.8258975
        },
        {
            "lat": 11.088421,
            "lng": 78.8264017
        },
        {
            "lat": 11.0876209,
            "lng": 78.826509
        },
        {
            "lat": 11.0874314,
            "lng": 78.8268952
        },
        {
            "lat": 11.0874629,
            "lng": 78.8277214
        },
        {
            "lat": 11.0883158,
            "lng": 78.8278823
        },
        {
            "lat": 11.0883579,
            "lng": 78.8269382
        },
        {
            "lat": 11.0886316,
            "lng": 78.8268523
        },
        {
            "lat": 11.0890843,
            "lng": 78.8260906
        },
        {
            "lat": 11.0892107,
            "lng": 78.8273351
        },
        {
            "lat": 11.0905583,
            "lng": 78.8272922
        },
        {
            "lat": 11.0903056,
            "lng": 78.8278501
        },
        {
            "lat": 11.0892107,
            "lng": 78.8278716
        },
        {
            "lat": 11.0890317,
            "lng": 78.8293414
        },
        {
            "lat": 11.090611,
            "lng": 78.8292985
        },
        {
            "lat": 11.0916322,
            "lng": 78.8294272
        },
        {
            "lat": 11.0924956,
            "lng": 78.8295345
        },
        {
            "lat": 11.0925377,
            "lng": 78.829159
        },
        {
            "lat": 11.0927693,
            "lng": 78.8291054
        },
        {
            "lat": 11.0934431,
            "lng": 78.8290303
        },
        {
            "lat": 11.0935905,
            "lng": 78.8289015
        }
    ],
    "Padalur 6": [
        {
            "lat": 11.1515904,
            "lng": 78.8707765
        },
        {
            "lat": 11.1522641,
            "lng": 78.8686308
        },
        {
            "lat": 11.1520115,
            "lng": 78.8661417
        },
        {
            "lat": 11.1518009,
            "lng": 78.8640817
        },
        {
            "lat": 11.1517588,
            "lng": 78.8616785
        },
        {
            "lat": 11.1519273,
            "lng": 78.8583311
        },
        {
            "lat": 11.1533588,
            "lng": 78.8564857
        },
        {
            "lat": 11.1547062,
            "lng": 78.8541254
        },
        {
            "lat": 11.1551482,
            "lng": 78.8527413
        },
        {
            "lat": 11.154093,
            "lng": 78.8520547
        },
        {
            "lat": 11.1531694,
            "lng": 78.8511749
        },
        {
            "lat": 11.1526009,
            "lng": 78.8551554
        },
        {
            "lat": 11.1512536,
            "lng": 78.8556703
        },
        {
            "lat": 11.1495273,
            "lng": 78.8570865
        },
        {
            "lat": 11.1486852,
            "lng": 78.8606914
        },
        {
            "lat": 11.1488957,
            "lng": 78.8625797
        },
        {
            "lat": 11.1479273,
            "lng": 78.865498
        },
        {
            "lat": 11.1455272,
            "lng": 78.8665279
        },
        {
            "lat": 11.1455272,
            "lng": 78.8681158
        },
        {
            "lat": 11.1458641,
            "lng": 78.8698324
        },
        {
            "lat": 11.1480536,
            "lng": 78.8698324
        },
        {
            "lat": 11.1491904,
            "lng": 78.8690599
        },
        {
            "lat": 11.1509588,
            "lng": 78.8682016
        },
        {
            "lat": 11.1502852,
            "lng": 78.8701328
        },
        {
            "lat": 11.149822,
            "lng": 78.8734373
        },
        {
            "lat": 11.1515483,
            "lng": 78.8753256
        },
        {
            "lat": 11.1528536,
            "lng": 78.8743814
        },
        {
            "lat": 11.1515904,
            "lng": 78.8707765
        }
    ],
    "Padalur 8": [
        {
            "lat": 11.1575461,
            "lng": 78.8956884
        },
        {
            "lat": 11.1564514,
            "lng": 78.8957956
        },
        {
            "lat": 11.1562829,
            "lng": 78.8967827
        },
        {
            "lat": 11.1561987,
            "lng": 78.8984135
        },
        {
            "lat": 11.1563251,
            "lng": 78.9011601
        },
        {
            "lat": 11.1553356,
            "lng": 78.9009455
        },
        {
            "lat": 11.1545356,
            "lng": 78.9005592
        },
        {
            "lat": 11.1536304,
            "lng": 78.8999155
        },
        {
            "lat": 11.1532514,
            "lng": 78.899186
        },
        {
            "lat": 11.1533356,
            "lng": 78.8970831
        },
        {
            "lat": 11.1529988,
            "lng": 78.8956454
        },
        {
            "lat": 11.1518198,
            "lng": 78.8958386
        },
        {
            "lat": 11.1513988,
            "lng": 78.8973406
        },
        {
            "lat": 11.1521988,
            "lng": 78.8982204
        },
        {
            "lat": 11.1523251,
            "lng": 78.9001515
        },
        {
            "lat": 11.1541777,
            "lng": 78.9018682
        },
        {
            "lat": 11.1539672,
            "lng": 78.9024046
        },
        {
            "lat": 11.1537356,
            "lng": 78.9036062
        },
        {
            "lat": 11.1545146,
            "lng": 78.9041427
        },
        {
            "lat": 11.1549146,
            "lng": 78.9036062
        },
        {
            "lat": 11.1555672,
            "lng": 78.9031342
        },
        {
            "lat": 11.1558198,
            "lng": 78.9044431
        },
        {
            "lat": 11.1565145,
            "lng": 78.9049152
        },
        {
            "lat": 11.1581987,
            "lng": 78.9049366
        },
        {
            "lat": 11.1590197,
            "lng": 78.9036491
        },
        {
            "lat": 11.1580513,
            "lng": 78.9024261
        },
        {
            "lat": 11.1581566,
            "lng": 78.9013746
        },
        {
            "lat": 11.1577566,
            "lng": 78.9005592
        },
        {
            "lat": 11.1577987,
            "lng": 78.8987139
        },
        {
            "lat": 11.1575461,
            "lng": 78.8965681
        },
        {
            "lat": 11.1575461,
            "lng": 78.8956884
        }
    ],

}

export const villageToFacilityMap = {
    "Sridevimangalam": "PBTN04467",
    "Padalur 3": "PBTN04460",
    "Padalur 1": "PBTN04458",
    "Padalur 7": "PBTN07999",
    "Padalur 4": "PBTN04461",
    "Padalur 5": "PBTN04462",
    "Padalur 6": "PBTN05211",
    "Padalur 8": "PBTN05211",
}

export const supportedFacilities = [
    "PBTN04467",
    "PBTN04460",
    "PBTN04458",
    "PBTN07999",
    "PBTN04461",
    "PBTN04462",
    "PBTN05211",
    "PBTN04459",
    "PBTN07998",
    "PBTN08000"
]