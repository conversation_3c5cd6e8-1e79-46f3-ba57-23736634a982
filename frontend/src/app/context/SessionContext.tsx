"use client";

import { createContext, useState, useContext, ReactNode, useEffect, useMemo, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { v4 as uuidv4 } from 'uuid';
import { CartItem, Session, DetailedCartItem, CartDetails } from '../types/session';
import * as cartService from '../services/cartService';
import { InternalCheckoutData, CheckoutResponse } from '../services/cartService';
import { isCustomerFacingMode } from '../../lib/utils';
import { customerDetailsRepository, CustomerDetails } from '../repository/CustomerDetailsRepository';

const LOCAL_STORAGE_SESSIONS_KEY = 'infinity-portal-sessions';
const LOCAL_STORAGE_ACTIVE_SESSION_ID_KEY = 'infinity-portal-active-session-id';

// Context type
interface SessionContextType {
  sessions: Session[];
  activeSessionId: string;
  setActiveSessionId: (id: string) => void;
  addSession: (sessionData?: Omit<Session, 'id' | 'cartItems' | 'lastActive'>) => Promise<string>;
  updateSession: (id: string, data: Partial<Omit<Session, 'id' | 'cartItems' | 'lastActive'>>) => void;
  removeSession: (id: string) => void;
  addItemToCartMutation: ReturnType<typeof useMutation<CartItem[], Error, CartItem>>;
  removeItemFromCartMutation: ReturnType<typeof useMutation<CartItem[], Error, { productId: number; variantId?: number }>>;
  updateCartItemQuantityMutation: ReturnType<typeof useMutation<CartItem[], Error, { productId: number; variantId?: number; newQuantity: number }>>;
  getCartItemsCount: () => number;
  getActiveSessionCart: () => CartItem[];
  cartDetailsQuery: ReturnType<typeof useQuery<CartDetails & { removedItems?: Array<{ item: CartItem; reason: string }> }, Error>>;
  validateCartMutation: ReturnType<typeof useMutation<{ validItems: CartItem[]; removedItems: Array<{ item: CartItem; reason: string }> }, Error, void>>;
  checkoutMutation: ReturnType<typeof useMutation<CheckoutResponse, Error, InternalCheckoutData>>;
  cartNotifications: Array<{ id: string; message: string; type: 'info' | 'warning' | 'error' }>;
  dismissCartNotification: (id: string) => void;

  // Renamed for clarity: these pertain to customer sessions, not user (Bhuvanesh) auth
  hasActiveCustomerSession: boolean;
  currentCustomerName?: string;
  startOrActivateCustomerSession: (data?: { customerName: string; customerPhone?: string; location?: { lat: number, lng: number } }) => Promise<void>;
  endActiveCustomerSession: () => Promise<void>;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export function SessionProvider({ children }: { children: ReactNode }) {
  const { t } = useTranslation();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [activeSessionId, setActiveSessionIdState] = useState<string>('');
  const [isClientMounted, setIsClientMounted] = useState(false);
  const [cartNotifications, setCartNotifications] = useState<Array<{ id: string; message: string; type: 'info' | 'warning' | 'error' }>>([]);
  const queryClient = useQueryClient();

  useEffect(() => {
    setIsClientMounted(true);
    try {
      const storedSessionsItem = localStorage.getItem(LOCAL_STORAGE_SESSIONS_KEY);
      if (storedSessionsItem && storedSessionsItem !== 'undefined' && storedSessionsItem !== 'null') {
        try {
          const parsedSessions: Session[] = JSON.parse(storedSessionsItem).map((s: Session) => ({
            ...s,
            lastActive: new Date(s.lastActive)
          }));
          setSessions(parsedSessions);
        } catch (parseError) {
          console.error('Failed to parse stored sessions from localStorage:', parseError, 'Raw value was:', storedSessionsItem);
          setSessions([]);
        }
      } else {
        setSessions([]);
      }
      const storedActiveSessionIdItem = localStorage.getItem(LOCAL_STORAGE_ACTIVE_SESSION_ID_KEY);
      if (storedActiveSessionIdItem && storedActiveSessionIdItem !== 'undefined' && storedActiveSessionIdItem !== 'null') {
        setActiveSessionIdState(storedActiveSessionIdItem);
      } else {
        setActiveSessionIdState('');
      }
    } catch (e) {
      console.error("Error during initial load from localStorage:", e);
      setSessions([]);
      setActiveSessionIdState('');
    }
  }, []);

  useEffect(() => {
    if (!isClientMounted) return;
    try {
      localStorage.setItem(LOCAL_STORAGE_SESSIONS_KEY, JSON.stringify(sessions));
    } catch (error) {
      console.error("Failed to save sessions to localStorage:", error);
    }
  }, [sessions, isClientMounted]);

  useEffect(() => {
    if (!isClientMounted) return;
    try {
      localStorage.setItem(LOCAL_STORAGE_ACTIVE_SESSION_ID_KEY, activeSessionId);
    } catch (error) {
      console.error("Failed to save active session ID to localStorage:", error);
    }
  }, [activeSessionId, isClientMounted]);

  const generateId = (): string => {
    return uuidv4();
  };

  const _addSessionInternal = (sessionData: Omit<Session, 'id' | 'cartItems' | 'lastActive'>): string => {
    const id = generateId();
    const newSession: Session = {
      id,
      ...sessionData,
      cartItems: [],
      lastActive: new Date()
    };
    setSessions(prevSessions => [...prevSessions, newSession]);
    return id;
  };

  const addSession = useCallback(async (sessionData?: Omit<Session, 'id' | 'cartItems' | 'lastActive'>): Promise<string> => {
    let dataToUse = sessionData;

    if (isCustomerFacingMode() && !sessionData) {
      // In customer mode, try to load saved customer details
      try {
        const savedDetails = await customerDetailsRepository.getCustomerDetails();
        if (savedDetails) {
          console.log('SessionContext: Using saved customer details for new session');
          dataToUse = {
            customerName: savedDetails.customerName,
            customerPhone: savedDetails.customerPhone,
            location: savedDetails.location
          };
        } else {
          console.log('SessionContext: No saved customer details found, using default');
          dataToUse = { customerName: t('session.defaultCustomerName', 'My Session') };
        }
      } catch (error) {
        console.error('SessionContext: Failed to load saved customer details:', error);
        dataToUse = { customerName: t('session.defaultCustomerName', 'My Session') };
      }
    }

    if (!dataToUse) {
      console.warn("addSession called without data in non-customer mode or without default fallback. Creating unnamed session.");
      dataToUse = { customerName: t('session.defaultCustomerName', 'My Session') };
    }

    return _addSessionInternal(dataToUse);
  }, [t]);

  useEffect(() => {
    const initializeDefaultSession = async () => {
      if (isClientMounted && sessions.length === 0 && !activeSessionId) {
        if (isCustomerFacingMode()) {
          console.log("SessionContext: Customer facing mode, no sessions found. Creating default session.");
          try {
            const newSessionId = await addSession();
            setActiveSessionIdState(newSessionId);
          } catch (error) {
            console.error('SessionContext: Failed to create default customer session:', error);
            // Fallback to basic session
            const newSessionId = _addSessionInternal({ customerName: t('session.defaultCustomerName', 'My Session') });
            setActiveSessionIdState(newSessionId);
          }
        } else {
          // Admin mode - create default Guest 1 session
          console.log("SessionContext: Admin mode, no sessions found. Creating default Guest 1 session.");
          const newSessionId = _addSessionInternal({ customerName: 'Guest 1' });
          setActiveSessionIdState(newSessionId);
        }
      }
    };

    initializeDefaultSession();
  }, [isClientMounted, sessions, activeSessionId, t, addSession]);

  const updateSession = useCallback((id: string, data: Partial<Omit<Session, 'id' | 'cartItems' | 'lastActive'>>) => {
    setSessions(prevSessions =>
      prevSessions.map(session =>
        session.id === id ? { ...session, ...data, lastActive: new Date() } : session
      )
    );

    // Sync cart if this is the active session and sync is enabled
    if (id === activeSessionId) {
      const updatedSession = sessions.find(s => s.id === id);
      if (updatedSession && updatedSession.cartSyncEnabled && updatedSession.cartItems.length > 0) {
        cartService.syncCartToBackend(
          id,
          updatedSession.cartItems,
          {
            customerName: data.customerName || updatedSession.customerName,
            customerPhone: data.customerPhone || updatedSession.customerPhone,
            landmark: data.landmark || updatedSession.landmark,
            location: data.location || updatedSession.location
          }
        );
      }
    }
  }, [activeSessionId, sessions]);

  const removeSession = useCallback((id: string) => {
    setSessions(prevSessions => {
      const updatedSessions = prevSessions.filter(session => session.id !== id);
      if (id === activeSessionId && updatedSessions.length > 0) {
        setActiveSessionIdState(updatedSessions[0].id);
      } else if (updatedSessions.length === 0) {
        setActiveSessionIdState('');
      }
      queryClient.invalidateQueries({ queryKey: ['cartDetails', id] });
      return updatedSessions;
    });
  }, [activeSessionId, queryClient]);

  const getActiveSession = (): Session | undefined => {
    return sessions.find(s => s.id === activeSessionId);
  };

  const cartDetailsQuery = useQuery<CartDetails & { removedItems?: Array<{ item: CartItem; reason: string }> }, Error>({
    queryKey: ['cartDetails', activeSessionId],
    queryFn: async () => {
      if (!activeSessionId) {
        return {
          detailedCartItems: [],
          itemsTotalPrice: 0,
          itemsTotalMRP: 0,
          totalSavings: 0,
          totalCartQuantity: 0,
        };
      }
      const session = getActiveSession();
      return cartService.getCartDetails(session?.cartItems || []);
    },
    enabled: !!activeSessionId && isClientMounted,
  });

  // Handle automatic cart cleanup when removed items are detected
  useEffect(() => {
    if (cartDetailsQuery.data?.removedItems && cartDetailsQuery.data.removedItems.length > 0) {
      const removedItems = cartDetailsQuery.data.removedItems;

      // Add notification for removed items
      setCartNotifications(prevNotifications => [
        ...prevNotifications,
        {
          id: generateId(),
          message: t('cart.itemsRemoved', `${removedItems.length} item(s) were removed from your cart because they are no longer available.`),
          type: 'warning'
        }
      ]);

      // Update cart to remove invalid items
      const session = getActiveSession();
      if (session) {
        const validCartItems = session.cartItems.filter(cartItem =>
          !removedItems.some(removed =>
            removed.item.skuId === cartItem.skuId &&
            removed.item.variantSkuId === cartItem.variantSkuId
          )
        );
        updateSessionCart(validCartItems);
      }
    }
  }, [cartDetailsQuery.data?.removedItems, t]);

  const updateSessionCart = (updatedCartItems: CartItem[], forceSync?: boolean) => {
    console.log('SessionContext: updateSessionCart called with:', updatedCartItems, 'forceSync:', forceSync);
    if (!activeSessionId) {
      console.log('SessionContext: No active session ID, returning');
      return;
    }

    const currentSession = getActiveSession();
    if (!currentSession) {
      console.log('SessionContext: No current session found, returning');
      return;
    }

    console.log('SessionContext: Updating session cart for session:', activeSessionId);
    // Update session with new cart items
    setSessions(prevSessions =>
      prevSessions.map(s =>
        s.id === activeSessionId
          ? { ...s, cartItems: updatedCartItems, lastActive: new Date() }
          : s
      )
    );

    // Sync to backend if sync is enabled for this session OR if forceSync is true
    if (currentSession.cartSyncEnabled || forceSync) {
      console.log(`SessionContext: Syncing cart to backend for session ${activeSessionId}:`, updatedCartItems);
      console.log('SessionContext: Customer data for sync:', {
        customerName: currentSession.customerName,
        customerPhone: currentSession.customerPhone,
        location: currentSession.location
      });
      console.log('SessionContext: cartSyncEnabled:', currentSession.cartSyncEnabled, 'forceSync:', forceSync);
      cartService.syncCartToBackend(
        activeSessionId,
        updatedCartItems,
        {
          customerName: currentSession.customerName,
          customerPhone: currentSession.customerPhone,
          landmark: currentSession.landmark,
          location: currentSession.location
        }
      ).catch(error => {
        console.error('SessionContext: Failed to sync cart to backend:', error);
      });
    } else {
      console.log('SessionContext: Cart sync not enabled, skipping backend sync');
      console.log('SessionContext: cartSyncEnabled:', currentSession.cartSyncEnabled, 'forceSync:', forceSync);
    }

    /// Wait for few ms to invalidate the query. DON'T REMOVE THIS.
    setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['cartDetails', activeSessionId] });
    }, 10);
  };

  const addItemToCartMutation = useMutation<
    CartItem[],
    Error,
    CartItem
  >({
    mutationFn: async (newItem: CartItem) => {
      console.log('SessionContext: addItemToCartMutation mutationFn called with:', newItem);
      const session = getActiveSession();
      console.log('SessionContext: Current session for mutation:', session);

      if (!session) {
        console.error('SessionContext: No active session found for addItemToCart');
        throw new Error('No active session found');
      }

      console.log('SessionContext: Calling cartService.addItemToCart with cart:', session.cartItems);
      const result = await cartService.addItemToCart(session.cartItems, newItem);
      console.log('SessionContext: cartService.addItemToCart returned:', result);
      return result;
    },
    onSuccess: (updatedCartItems) => {
      console.log('SessionContext: addItemToCartMutation onSuccess called with:', updatedCartItems);
      const currentSession = getActiveSession();
      console.log('SessionContext: Current session in onSuccess:', currentSession);

      let shouldEnableSync = false;
      // Enable cart sync when first item is added to a session
      if (currentSession && !currentSession.cartSyncEnabled && updatedCartItems.length > 0) {
        console.log(`SessionContext: Enabling cart sync for session ${activeSessionId}`);
        shouldEnableSync = true;
        setSessions(prevSessions =>
          prevSessions.map(s =>
            s.id === activeSessionId
              ? { ...s, cartSyncEnabled: true }
              : s
          )
        );
        console.log(`SessionContext: Cart sync enabled for session ${activeSessionId}`);
      }

      console.log('SessionContext: Calling updateSessionCart with:', updatedCartItems);
      updateSessionCart(updatedCartItems, shouldEnableSync);
    },
    onError: (error, variables) => {
      console.error('SessionContext: addItemToCartMutation onError called:', error);
      console.error('SessionContext: Failed mutation variables:', variables);
      console.error('SessionContext: Error details:', error.message, error.stack);
    },
  });

  const removeItemFromCartMutation = useMutation<
    CartItem[],
    Error,
    { productId: number; variantId?: number }
  >({
    mutationFn: async ({ productId, variantId }) => {
      const session = getActiveSession();
      return cartService.removeItemFromCart(session?.cartItems || [], productId, variantId);
    },
    onSuccess: (updatedCartItems) => {
      updateSessionCart(updatedCartItems);
    },
  });

  const updateCartItemQuantityMutation = useMutation<
    CartItem[],
    Error,
    { productId: number; variantId?: number; newQuantity: number }
  >({
    mutationFn: async ({ productId, variantId, newQuantity }) => {
      const session = getActiveSession();
      return cartService.updateCartItemQuantity(session?.cartItems || [], productId, variantId, newQuantity);
    },
    onSuccess: (updatedCartItems) => {
      updateSessionCart(updatedCartItems);
    },
  });

  const getCartItemsCount = useCallback((): number => {
    return cartDetailsQuery.data?.totalCartQuantity || 0;
  }, [cartDetailsQuery.data?.totalCartQuantity]);

  const getActiveSessionCart = useCallback((): CartItem[] => {
    const session = sessions.find(s => s.id === activeSessionId);
    if (!session) return [];
    return session.cartItems;
  }, [sessions, activeSessionId]);

  const startOrActivateCustomerSession = useCallback(async (data?: { customerName: string; customerPhone?: string; location?: { lat: number, lng: number } }) => {
    console.log('SessionContext: startOrActivateCustomerSession called', data);
    if (isCustomerFacingMode()) {
      if (!activeSessionId && sessions.length > 0) {
        setActiveSessionIdState(sessions[0].id);
      } else if (!activeSessionId && sessions.length === 0) {
        console.warn("SessionContext: startOrActivateCustomerSession in customer mode, but no session exists. Creating default.");
        try {
          const newSessionId = await addSession({
            customerName: data?.customerName,
            customerPhone: data?.customerPhone,
            location: data?.location
          });
          setActiveSessionIdState(newSessionId);
        } catch (error) {
          console.error('SessionContext: Failed to create customer session:', error);
          // Fallback to internal method
          const defaultSessionName = t('session.defaultCustomerName', 'My Session');
          const newSessionId = _addSessionInternal({
            customerName: data?.customerName || defaultSessionName,
            customerPhone: data?.customerPhone,
            location: data?.location
          });
          setActiveSessionIdState(newSessionId);
        }
      } else if (activeSessionId && data) {
        updateSession(activeSessionId, {
          customerName: data.customerName,
          customerPhone: data.customerPhone,
          location: data.location
        });
      }
      return;
    }

    if (!activeSessionId && sessions.length > 0) {
      setActiveSessionIdState(sessions[0].id);
    } else if (!activeSessionId && sessions.length === 0) {
      const newSessionId = _addSessionInternal({
        customerName: data?.customerName || t('session.guestCustomerName', 'Guest Customer'),
        customerPhone: data?.customerPhone || undefined,
        location: data?.location || undefined,
      });
      setActiveSessionIdState(newSessionId);
    } else if (activeSessionId && data) {
      updateSession(activeSessionId, {
        customerName: data.customerName,
        customerPhone: data.customerPhone,
        location: data.location
      });
    }
  }, [activeSessionId, sessions, addSession, updateSession, t]);

  const endActiveCustomerSession = useCallback(async () => {
    console.log('SessionContext: endActiveCustomerSession called');

    if (isCustomerFacingMode()) {
      // In customer mode, automatically create a new session with saved details
      try {
        console.log('SessionContext: Creating new session with saved customer details');
        const newSessionId = await addSession();
        setActiveSessionIdState(newSessionId);
        console.log('SessionContext: New customer session created:', newSessionId);
      } catch (error) {
        console.error('SessionContext: Failed to create new customer session:', error);
        // Fallback to clearing session
        setActiveSessionIdState('');
      }
    } else {
      // In admin mode, just clear the session
      setActiveSessionIdState('');
    }
  }, [addSession]);

  const hasActiveCustomerSession = !!activeSessionId && !!sessions.find(s => s.id === activeSessionId);
  const currentCustomerDetails = sessions.find(s => s.id === activeSessionId);
  const currentCustomerName = currentCustomerDetails?.customerName;

  const validateCartMutation = useMutation<
    { validItems: CartItem[]; removedItems: Array<{ item: CartItem; reason: string }> },
    Error,
    void
  >({
    mutationFn: async () => {
      const session = getActiveSession();
      return cartService.validateAndCleanupCart(session?.cartItems || []);
    },
    onSuccess: (result) => {
      if (result.removedItems.length > 0) {
        setCartNotifications(prevNotifications => [
          ...prevNotifications,
          {
            id: generateId(),
            message: `Removed items due to unavailability: ${result.removedItems.map(item => item.reason).join(', ')}`,
            type: 'warning'
          }
        ]);
      }
      updateSessionCart(result.validItems);
    },
  });

  const setActiveSessionId = useCallback((id: string) => {
    // Sync current session before switching if it has items and sync is enabled
    const currentSession = getActiveSession();
    setActiveSessionIdState(id);
    queryClient.invalidateQueries({ queryKey: ['cartDetails', activeSessionId] });
    queryClient.invalidateQueries({ queryKey: ['cartDetails', id] });

    // Auto-validate cart when switching sessions (after a small delay to let the query update)
    setTimeout(() => {
      const newSession = sessions.find(s => s.id === id);
      if (newSession && newSession.cartItems.length > 0) {
        validateCartMutation.mutate();
      }
    }, 100);
  }, [activeSessionId, sessions, queryClient, validateCartMutation]);

  const dismissCartNotification = useCallback((id: string) => {
    setCartNotifications(prevNotifications => prevNotifications.filter(n => n.id !== id));
  }, []);

  const checkoutMutation = useMutation<CheckoutResponse, Error, InternalCheckoutData>({
    mutationFn: async (checkoutData: InternalCheckoutData) => {
      return cartService.processCheckout(checkoutData);
    },
    onSuccess: async (response, checkoutData) => {
      console.log('SessionContext: Checkout successful, response:', response);

      // In customer mode, save customer details for future sessions
      if (isCustomerFacingMode() && checkoutData) {
        try {
          const customerDetails: CustomerDetails = {
            customerName: checkoutData.customerName,
            customerPhone: checkoutData.customerPhone,
            location: checkoutData.location
          };

          await customerDetailsRepository.saveCustomerDetails(customerDetails);
          console.log('SessionContext: Customer details saved successfully after checkout');
        } catch (error) {
          console.error('SessionContext: Failed to save customer details after checkout:', error);
          // Don't fail the checkout for this, just log the error
        }
      }

      // Don't clear the session here - let the UI handle it after showing success
    },
    onError: (error) => {
      console.error('Checkout failed:', error);
      setCartNotifications(prevNotifications => [
        ...prevNotifications,
        {
          id: generateId(),
          message: error.message || 'Checkout failed. Please try again.',
          type: 'error'
        }
      ]);
    },
  });

  const value = useMemo(() => ({
    sessions,
    activeSessionId,
    setActiveSessionId,
    addSession,
    updateSession,
    removeSession,
    addItemToCartMutation,
    removeItemFromCartMutation,
    updateCartItemQuantityMutation,
    getCartItemsCount,
    getActiveSessionCart,
    cartDetailsQuery,
    validateCartMutation,
    checkoutMutation,
    cartNotifications,
    dismissCartNotification,
    hasActiveCustomerSession,
    currentCustomerName,
    startOrActivateCustomerSession,
    endActiveCustomerSession,
  }), [
    sessions,
    activeSessionId,
    setActiveSessionId,
    addSession,
    updateSession,
    removeSession,
    addItemToCartMutation,
    removeItemFromCartMutation,
    updateCartItemQuantityMutation,
    getCartItemsCount,
    getActiveSessionCart,
    cartDetailsQuery,
    validateCartMutation,
    checkoutMutation,
    cartNotifications,
    dismissCartNotification,
    hasActiveCustomerSession,
    currentCustomerName,
    startOrActivateCustomerSession,
    endActiveCustomerSession,
  ]);

  if (!isClientMounted) {
    return null;
  }

  return <SessionContext.Provider value={value}>{children}</SessionContext.Provider>;
}

export function useSession() {
  const context = useContext(SessionContext);
  if (context === undefined) {
    throw new Error('useSession must be used within a SessionProvider');
  }
  return context;
}

