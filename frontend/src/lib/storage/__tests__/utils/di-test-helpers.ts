/**
 * Test helpers for dependency injection and mocking.
 * 
 * This module provides utilities to set up mock environments and dependencies
 * for testing the storage system with the new dependency injection architecture.
 */

import { 
    MockEnvironmentDetector, 
    setEnvironmentDetector, 
    resetEnvironmentDetector 
} from '../../environment/EnvironmentDetector';
import { 
    getStorageContainer, 
    resetStorageContainer,
    registerMockDependency,
    registerMockAdapter
} from '../../di/StorageContainer';
import { StorageAdapterFactory } from '../../StorageFactory';
import type { PlatformInfo, StorageConfig, IStorageAdapter } from '../../types';

/**
 * Mock localforage implementation for testing
 */
export function createMockLocalforage() {
    const stores = new Map<string, Map<string, any>>();

    const createInstance = (config?: any) => {
        const instanceKey = config ? `${config.name || 'default'}_${config.storeName || 'default'}` : 'default_default';
        
        if (!stores.has(instanceKey)) {
            stores.set(instanceKey, new Map());
        }
        const store = stores.get(instanceKey)!;

        return {
            ready: jest.fn().mockResolvedValue(undefined),
            getItem: jest.fn((key) => Promise.resolve(store.get(key) || null)),
            setItem: jest.fn((key, value) => {
                store.set(key, value);
                return Promise.resolve(value);
            }),
            removeItem: jest.fn((key) => {
                store.delete(key);
                return Promise.resolve();
            }),
            clear: jest.fn(() => {
                store.clear();
                return Promise.resolve();
            }),
            keys: jest.fn(() => Promise.resolve(Array.from(store.keys()))),
            length: jest.fn(() => Promise.resolve(store.size)),
            driver: jest.fn(() => 'asyncStorage'),
            config: jest.fn(() => config || {}),
            setDriver: jest.fn(() => Promise.resolve())
        };
    };

    return {
        createInstance: jest.fn(createInstance),
        INDEXEDDB: 'asyncStorage',
        LOCALSTORAGE: 'localStorageWrapper',
        WEBSQL: 'webSQLStorage',
        supports: jest.fn(() => true),
        ...createInstance()
    };
}

/**
 * Mock Node.js fs module for testing
 */
export function createMockFs() {
    const files = new Map<string, string>();
    const directories = new Set<string>();

    return {
        mkdir: jest.fn(async (path: string, options?: any) => {
            // Always succeed for directory creation
            directories.add(path);
            return undefined;
        }),
        access: jest.fn(async (path: string) => {
            if (!files.has(path) && !directories.has(path)) {
                const error = new Error('ENOENT: no such file or directory') as any;
                error.code = 'ENOENT';
                throw error;
            }
            return undefined;
        }),
        readFile: jest.fn(async (path: string, encoding?: string) => {
            const content = files.get(path);
            if (content === undefined) {
                const error = new Error('ENOENT: no such file or directory') as any;
                error.code = 'ENOENT';
                throw error;
            }
            return content;
        }),
        writeFile: jest.fn(async (path: string, data: string, encoding?: string) => {
            files.set(path, data);
            return undefined;
        }),
        rename: jest.fn(async (oldPath: string, newPath: string) => {
            const content = files.get(oldPath);
            if (content !== undefined) {
                files.set(newPath, content);
                files.delete(oldPath);
            } else {
                const error = new Error('ENOENT: no such file or directory') as any;
                error.code = 'ENOENT';
                throw error;
            }
            return undefined;
        }),
        unlink: jest.fn(async (path: string) => {
            if (files.has(path)) {
                files.delete(path);
            } else {
                const error = new Error('ENOENT: no such file or directory') as any;
                error.code = 'ENOENT';
                throw error;
            }
            return undefined;
        }),
        stat: jest.fn(async (path: string) => {
            if (!files.has(path) && !directories.has(path)) {
                const error = new Error('ENOENT: no such file or directory') as any;
                error.code = 'ENOENT';
                throw error;
            }
            return {
                size: files.get(path)?.length || 0,
                birthtime: new Date(),
                mtime: new Date(),
                atime: new Date(),
                isDirectory: () => directories.has(path),
                isFile: () => files.has(path)
            };
        }),
        copyFile: jest.fn(async (src: string, dest: string) => {
            const content = files.get(src);
            if (content !== undefined) {
                files.set(dest, content);
            } else {
                const error = new Error('ENOENT: no such file or directory') as any;
                error.code = 'ENOENT';
                throw error;
            }
            return undefined;
        }),
        // Expose internal state for testing
        _getFiles: () => files,
        _getDirectories: () => directories,
        _reset: () => {
            files.clear();
            directories.clear();
        }
    };
}

/**
 * Mock Node.js path module for testing
 */
export function createMockPath() {
    return {
        join: jest.fn((...paths: string[]) => paths.join('/')),
        resolve: jest.fn((...paths: string[]) => '/' + paths.join('/'))
    };
}

/**
 * Mock AsyncStorage for React Native testing
 */
export function createMockAsyncStorage() {
    const storage = new Map<string, string>();

    return {
        getItem: jest.fn(async (key: string) => storage.get(key) || null),
        setItem: jest.fn(async (key: string, value: string) => {
            storage.set(key, value);
        }),
        removeItem: jest.fn(async (key: string) => {
            storage.delete(key);
        }),
        clear: jest.fn(async () => {
            storage.clear();
        }),
        getAllKeys: jest.fn(async () => Array.from(storage.keys()))
    };
}

/**
 * Set up a mock browser environment for testing
 */
export function setupMockBrowserEnvironment() {
    const platform: PlatformInfo = {
        platform: 'browser',
        isServer: false,
        isBrowser: true,
        isReactNative: false,
        details: {
            userAgent: 'Mozilla/5.0 (Test Browser)'
        }
    };

    const detector = new MockEnvironmentDetector(platform, {
        isBrowser: true,
        isLocalStorageAvailable: true,
        isIndexedDBAvailable: true
    });

    setEnvironmentDetector(detector);
    
    const mockLocalforage = createMockLocalforage();
    registerMockDependency('localforage', mockLocalforage);

    return () => {
        resetEnvironmentDetector();
        resetStorageContainer();
        StorageAdapterFactory.reset();
    };
}

/**
 * Set up a mock Node.js environment for testing
 */
export function setupMockNodeEnvironment() {
    const platform: PlatformInfo = {
        platform: 'node',
        isServer: true,
        isBrowser: false,
        isReactNative: false,
        details: {
            nodeVersion: '18.0.0'
        }
    };

    const detector = new MockEnvironmentDetector(platform, {
        isNode: true
    });

    setEnvironmentDetector(detector);
    
    const mockFs = createMockFs();
    const mockPath = createMockPath();
    registerMockDependency('fs', mockFs);
    registerMockDependency('path', mockPath);

    return () => {
        resetEnvironmentDetector();
        resetStorageContainer();
        StorageAdapterFactory.reset();
    };
}

/**
 * Set up a mock SSR environment for testing
 */
export function setupMockSSREnvironment() {
    const platform: PlatformInfo = {
        platform: 'node',
        isServer: true,
        isBrowser: false,
        isReactNative: false,
        details: {
            nodeVersion: '18.0.0',
            isSSR: true
        }
    };

    const detector = new MockEnvironmentDetector(platform, {
        isNode: true,
        isSSR: true
    });

    setEnvironmentDetector(detector);

    return () => {
        resetEnvironmentDetector();
        resetStorageContainer();
        StorageAdapterFactory.reset();
    };
}

/**
 * Set up a mock React Native environment for testing
 */
export function setupMockReactNativeEnvironment() {
    const platform: PlatformInfo = {
        platform: 'react-native',
        isServer: false,
        isBrowser: false,
        isReactNative: true,
        details: {
            reactNativeVersion: '0.72.0'
        }
    };

    const detector = new MockEnvironmentDetector(platform, {
        isReactNative: true,
        isAsyncStorageAvailable: true
    });

    setEnvironmentDetector(detector);
    
    const mockAsyncStorage = createMockAsyncStorage();
    registerMockDependency('asyncStorage', mockAsyncStorage);

    return () => {
        resetEnvironmentDetector();
        resetStorageContainer();
        StorageAdapterFactory.reset();
    };
}

/**
 * Create a mock storage adapter for testing
 */
export function createMockStorageAdapter(): IStorageAdapter {
    const storage = new Map<string, any>();

    return {
        initialize: jest.fn().mockResolvedValue(undefined),
        getItem: jest.fn(async (key: string) => storage.get(key) || null),
        setItem: jest.fn(async (key: string, value: any) => {
            storage.set(key, value);
        }),
        removeItem: jest.fn(async (key: string) => {
            storage.delete(key);
        }),
        clear: jest.fn(async () => {
            storage.clear();
        }),
        keys: jest.fn(async () => Array.from(storage.keys())),
        length: jest.fn(async () => storage.size),
        getUsage: jest.fn(async () => ({ used: 1000, available: 9000, total: 10000 })),
        getCapabilities: jest.fn(() => ({
            supportsTTL: true,
            supportsTransactions: false,
            supportsBulkOperations: false,
            maxStorageSize: -1,
            persistent: false,
            platform: 'memory'
        })),
        dispose: jest.fn().mockResolvedValue(undefined)
    } as any;
}
