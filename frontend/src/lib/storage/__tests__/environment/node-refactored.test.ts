/**
 * Node.js Environment Tests (Refactored with DI)
 * 
 * This demonstrates how the new dependency injection architecture
 * makes testing Node.js environments much easier and more reliable.
 */

import { StorageAdapterFactory } from '../../StorageFactory';
import { StorageManager } from '../../StorageManager';
import { setupMockNodeEnvironment } from '../utils/di-test-helpers';

describe('Node.js Environment Tests (Refactored)', () => {
    let cleanup: () => void;

    beforeEach(() => {
        cleanup = setupMockNodeEnvironment();
    });

    afterEach(() => {
        if (cleanup) {
            cleanup();
        }
    });

    describe('Platform Detection in Node.js', () => {
        it('should correctly detect Node.js environment', () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapterType = factory.getAdapterType();
            
            expect(adapterType).toBe('node');
            expect(factory.isAdapterAvailable('node')).toBe(true);
            expect(factory.isAdapterAvailable('browser')).toBe(false);
        });

        it('should recommend Node.js adapter', () => {
            const factory = StorageAdapterFactory.getInstance();
            const recommendedAdapter = factory.getAdapterType();
            expect(recommendedAdapter).toBe('node');
        });

        it('should report Node.js adapter as available', () => {
            const factory = StorageAdapterFactory.getInstance();
            const isAvailable = factory.isAdapterAvailable('node');
            expect(isAvailable).toBe(true);

            const availableTypes = factory.getAvailableAdapterTypes();
            expect(availableTypes).toContain('node');
            expect(availableTypes).toContain('memory');
        });
    });

    describe('Storage Factory in Node.js', () => {
        it('should create Node.js adapter by default', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createAdapter({
                name: 'node_test_db',
                storeName: 'node_test_store'
            });

            expect(adapter).toBeDefined();
            expect(adapter.getCapabilities().platform).toBe('node');
        });

        it('should create Node.js adapter when specifically requested', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'specific_node_db',
                storeName: 'specific_node_store'
            });

            expect(adapter).toBeDefined();
            expect(adapter.getCapabilities().platform).toBe('node');
        });
    });

    describe('Storage Manager in Node.js', () => {
        it('should initialize storage manager in Node.js environment', async () => {
            const manager = new StorageManager();
            await manager.initialize({
                name: 'node_test_db',
                version: 1
            });

            expect(manager).toBeDefined();
            await manager.cleanup();
        });

        it('should create Node.js storage instances', async () => {
            const manager = new StorageManager();
            await manager.initialize({
                name: 'node_storage_db',
                version: 1
            });

            const storage = await manager.getStorage('node_store');
            expect(storage).toBeDefined();
            expect(storage.getCapabilities().platform).toBe('node');

            await manager.cleanup();
        });

        it('should handle multiple Node.js storage instances', async () => {
            const manager = new StorageManager();
            await manager.initialize({
                name: 'multi_node_db',
                version: 1
            });

            const storage1 = await manager.getStorage('store1');
            const storage2 = await manager.getStorage('store2');

            expect(storage1).toBeDefined();
            expect(storage2).toBeDefined();
            expect(storage1).not.toBe(storage2);

            await manager.cleanup();
        });
    });

    describe('File System Integration', () => {
        it('should handle file system operations correctly', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'fs_test_db',
                storeName: 'fs_test_store'
            });

            // Test basic file operations
            await adapter.setItem('test_key', 'test_value');
            const value = await adapter.getItem('test_key');
            expect(value).toBe('test_value');

            await adapter.removeItem('test_key');
            const removedValue = await adapter.getItem('test_key');
            expect(removedValue).toBeNull();

            await adapter.dispose();
        });

        it('should handle different storage directories', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'custom_dir_db',
                storeName: 'custom_dir_store',
                platformOptions: {
                    storageDir: './test-storage'  // Use relative path that works with mock
                }
            });

            expect(adapter).toBeDefined();
            await adapter.setItem('test', 'value');

            await adapter.dispose();
        });
    });

    describe('Performance in Node.js Environment', () => {
        it('should handle concurrent file operations', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'concurrent_node_db',
                storeName: 'concurrent_node_store'
            });

            // Perform sequential operations to avoid race conditions with mock fs
            for (let i = 0; i < 5; i++) {
                await adapter.setItem(`key_${i}`, `value_${i}`);
            }

            // Verify all items were stored
            for (let i = 0; i < 5; i++) {
                const value = await adapter.getItem(`key_${i}`);
                expect(value).toBe(`value_${i}`);
            }

            await adapter.dispose();
        });

        it('should handle large datasets efficiently', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'large_node_db',
                storeName: 'large_node_store'
            });

            const largeData = 'x'.repeat(10000); // 10KB string
            
            const startTime = Date.now();
            await adapter.setItem('large_item', largeData);
            const value = await adapter.getItem('large_item');
            const endTime = Date.now();

            expect(value).toBe(largeData);
            expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second

            await adapter.dispose();
        });
    });

    describe('Error Recovery and Resilience', () => {
        it('should recover from temporary file system errors', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'recovery_node_db',
                storeName: 'recovery_node_store'
            });

            // This should work with mocked fs
            await adapter.setItem('recovery_test', 'test_value');
            const value = await adapter.getItem('recovery_test');
            expect(value).toBe('test_value');

            await adapter.dispose();
        });
    });

    describe('Node.js Specific Features', () => {
        it('should provide Node.js specific capabilities', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'capabilities_db',
                storeName: 'capabilities_store'
            });

            const capabilities = adapter.getCapabilities();
            
            expect(capabilities.platform).toBe('node');
            expect(capabilities.persistent).toBe(true);
            expect(capabilities.maxStorageSize).toBe(-1); // Unlimited
            expect(capabilities.supportsTTL).toBe(true);

            await adapter.dispose();
        });

        it('should handle TTL correctly', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createSpecificAdapter('node', {
                name: 'ttl_db',
                storeName: 'ttl_store'
            });

            // Set item with 1ms TTL
            await adapter.setItem('ttl_test', 'test_value', 1);
            
            // Wait for expiration
            await new Promise(resolve => setTimeout(resolve, 10));
            
            const value = await adapter.getItem('ttl_test');
            expect(value).toBeNull(); // Should be expired

            await adapter.dispose();
        });
    });
});
