#!/usr/bin/env node

/**
 * Test Runner Script for Storage Module
 * 
 * This script provides convenient ways to run different test suites
 * and generate coverage reports for the storage module.
 */

const { execSync } = require('child_process');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(command, description) {
  log(`\n${description}`, 'cyan');
  log(`Running: ${command}`, 'yellow');
  
  try {
    execSync(command, { 
      stdio: 'inherit', 
      cwd: process.cwd(),
      env: { ...process.env, FORCE_COLOR: '1' }
    });
    log(`✅ ${description} completed successfully`, 'green');
    return true;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    return false;
  }
}

function showHelp() {
  log('\n📦 Storage Module Test Runner', 'bright');
  log('=====================================', 'bright');
  log('\nUsage: node run-tests.js [command]', 'cyan');
  log('\nAvailable commands:', 'yellow');
  log('  all              Run all tests', 'white');
  log('  unit             Run unit tests only', 'white');
  log('  integration      Run integration tests only', 'white');
  log('  environment      Run environment tests only', 'white');
  log('  adapters         Run adapter tests only', 'white');
  log('  browser          Run browser environment tests', 'white');
  log('  node             Run Node.js environment tests', 'white');
  log('  react            Run React environment tests', 'white');
  log('  ssr              Run SSR environment tests', 'white');
  log('  coverage         Run all tests with coverage report', 'white');
  log('  watch            Run tests in watch mode', 'white');
  log('  ci               Run tests in CI mode (no watch, with coverage)', 'white');
  log('  help             Show this help message', 'white');
  log('\nExamples:', 'yellow');
  log('  node run-tests.js all', 'white');
  log('  node run-tests.js unit', 'white');
  log('  node run-tests.js coverage', 'white');
  log('  node run-tests.js watch', 'white');
}

function getTestCommand(pattern, options = {}) {
  const baseCommand = 'npm test --';
  const parts = [baseCommand];
  
  if (pattern) {
    parts.push(`--testPathPattern="${pattern}"`);
  }
  
  if (options.coverage) {
    parts.push('--coverage');
  }
  
  if (options.watch) {
    parts.push('--watch');
  }
  
  if (options.ci) {
    parts.push('--ci --watchAll=false --coverage');
  }
  
  if (options.verbose) {
    parts.push('--verbose');
  }
  
  return parts.join(' ');
}

function runTests() {
  const command = process.argv[2] || 'help';
  
  log('\n🧪 Storage Module Test Suite', 'bright');
  log('==============================', 'bright');
  
  switch (command.toLowerCase()) {
    case 'all':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__'),
        'Running all storage tests'
      );
      
    case 'unit':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/unit'),
        'Running unit tests'
      );
      
    case 'integration':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/integration'),
        'Running integration tests'
      );
      
    case 'environment':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/environment'),
        'Running environment tests'
      );
      
    case 'adapters':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/unit/adapters'),
        'Running adapter tests'
      );
      
    case 'browser':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/environment/browser'),
        'Running browser environment tests'
      );
      
    case 'node':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/environment/node'),
        'Running Node.js environment tests'
      );
      
    case 'react':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/environment/react'),
        'Running React environment tests'
      );
      
    case 'ssr':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__/environment/ssr'),
        'Running SSR environment tests'
      );
      
    case 'coverage':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__', { coverage: true, verbose: true }),
        'Running all tests with coverage report'
      );
      
    case 'watch':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__', { watch: true }),
        'Running tests in watch mode'
      );
      
    case 'ci':
      return runCommand(
        getTestCommand('src/lib/storage/__tests__', { ci: true }),
        'Running tests in CI mode'
      );
      
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      return true;
      
    default:
      log(`❌ Unknown command: ${command}`, 'red');
      log('Use "help" to see available commands', 'yellow');
      return false;
  }
}

function showTestSummary() {
  log('\n📊 Test Suite Summary', 'bright');
  log('=====================', 'bright');
  log('\n📁 Test Structure:', 'cyan');
  log('  • Unit Tests: Individual component testing', 'white');
  log('    - Storage Adapters (Browser, Node.js, Memory, React Native)', 'white');
  log('    - Storage Manager', 'white');
  log('    - Storage Factory', 'white');
  log('    - Platform Detection', 'white');
  log('  • Environment Tests: Platform-specific testing', 'white');
  log('    - Browser Environment', 'white');
  log('    - Node.js Environment', 'white');
  log('    - React Integration', 'white');
  log('    - Server-Side Rendering', 'white');
  log('  • Integration Tests: End-to-end scenarios', 'white');
  log('    - Cross-Platform Compatibility', 'white');
  log('    - Storage Manager Integration', 'white');
  log('    - Repository Pattern Integration', 'white');
  
  log('\n🎯 Coverage Goals:', 'cyan');
  log('  • Line Coverage: 100%', 'white');
  log('  • Branch Coverage: 100%', 'white');
  log('  • Function Coverage: 100%', 'white');
  log('  • Statement Coverage: 100%', 'white');
  
  log('\n🚀 Quick Start:', 'cyan');
  log('  node run-tests.js all      # Run all tests', 'white');
  log('  node run-tests.js coverage # Run with coverage', 'white');
  log('  node run-tests.js watch    # Run in watch mode', 'white');
}

// Main execution
if (require.main === module) {
  const success = runTests();
  
  if (process.argv[2] === 'help' || process.argv[2] === '--help' || process.argv[2] === '-h') {
    showTestSummary();
  }
  
  process.exit(success ? 0 : 1);
}

module.exports = {
  runCommand,
  getTestCommand,
  showHelp,
  showTestSummary
};
