/**
 * Test to verify StorageFactory handles browser environments safely
 * without throwing errors when require() is undefined
 */

import { StorageAdapterFactory } from '../StorageFactory';
import { setupMockBrowserEnvironment } from './utils/di-test-helpers';

describe('StorageFactory Browser Safety', () => {
  let cleanup: () => void;
  let originalRequire: any;

  beforeEach(() => {
    cleanup = setupMockBrowserEnvironment();
    
    // Simulate browser environment where require is undefined
    originalRequire = (global as any).require;
    (global as any).require = undefined;
    
    // Reset factory to ensure clean state
    StorageAdapterFactory.reset();
  });

  afterEach(() => {
    // Restore require
    if (originalRequire) {
      (global as any).require = originalRequire;
    }
    
    cleanup();
  });

  it('should not throw errors when require is undefined in browser', async () => {
    expect(() => {
      const factory = StorageAdapterFactory.getInstance();
      expect(factory).toBeDefined();
    }).not.toThrow();
  });

  it('should successfully create adapters even when require is undefined', async () => {
    const factory = StorageAdapterFactory.getInstance();
    
    const config = {
      name: 'browser_safety_test',
      storeName: 'test_store',
      version: 1,
      description: 'Test browser safety'
    };

    // Should not throw and should fallback to memory adapter if needed
    const adapter = await factory.createAdapter(config);
    expect(adapter).toBeDefined();
    expect(adapter.getCapabilities).toBeDefined();
    
    await adapter.dispose();
  });

  it('should handle dependency initialization gracefully', async () => {
    const factory = StorageAdapterFactory.getInstance();
    
    // This should not throw even if dependencies can't be loaded
    expect(async () => {
      await (factory as any).ensureDependenciesInitialized();
    }).not.toThrow();
  });

  it('should provide fallback behavior when platform dependencies fail', async () => {
    const factory = StorageAdapterFactory.getInstance();
    
    const config = {
      name: 'fallback_test',
      storeName: 'fallback_store',
      version: 1,
      description: 'Test fallback behavior'
    };

    // Should create some adapter (likely memory) even if browser deps fail
    const adapter = await factory.createAdapterWithFallback(config);
    expect(adapter).toBeDefined();
    
    // Should be able to perform basic operations
    await adapter.setItem('test_key', 'test_value');
    const value = await adapter.getItem('test_key');
    expect(value).toBe('test_value');
    
    await adapter.dispose();
  });

  it('should handle multiple adapter creation attempts safely', async () => {
    const factory = StorageAdapterFactory.getInstance();
    
    const config = {
      name: 'multi_test',
      storeName: 'multi_store',
      version: 1,
      description: 'Test multiple adapter creation'
    };

    // Create multiple adapters - should not fail
    const adapters = await Promise.all([
      factory.createAdapter(config),
      factory.createAdapter({ ...config, storeName: 'multi_store_2' }),
      factory.createAdapter({ ...config, storeName: 'multi_store_3' })
    ]);

    expect(adapters).toHaveLength(3);
    adapters.forEach(adapter => {
      expect(adapter).toBeDefined();
    });

    // Cleanup
    await Promise.all(adapters.map(adapter => adapter.dispose()));
  });
});
