/**
 * Memory storage adapter for testing and fallback scenarios.
 * 
 * This adapter provides storage functionality using in-memory storage.
 * Data is not persistent and will be lost when the process ends.
 * Useful for testing, SSR, and fallback scenarios.
 */

import { BaseStorageAdapter } from '../IStorageAdapter';
import type { StorageCapabilities, StoredItemWithTimestamp } from '../types';

export class MemoryStorageAdapter extends BaseStorageAdapter {
    private store: Map<string, StoredItemWithTimestamp<any>> = new Map();
    private maxSize: number;

    constructor(maxSize: number = 10 * 1024 * 1024) { // 10MB default
        super();
        this.maxSize = maxSize;
    }

    protected async initializeStorage(): Promise<void> {
        // Clear any existing data
        this.store.clear();
        
        // Apply max size from config if provided
        if (this.config.platformOptions?.maxSize) {
            this.maxSize = this.config.platformOptions.maxSize;
        }
    }

    protected async getStoredItem<T>(key: string): Promise<StoredItemWithTimestamp<T> | null> {
        const item = this.store.get(this.getSafeKey(key));
        return item || null;
    }

    protected async setStoredItem<T>(key: string, item: StoredItemWithTimestamp<T>): Promise<void> {
        const safeKey = this.getSafeKey(key);
        
        // Check storage size limits
        if (this.maxSize > 0) {
            const estimatedSize = this.estimateItemSize(item);
            const currentSize = this.getCurrentSize();
            
            // Remove the old item size if it exists
            const existingItem = this.store.get(safeKey);
            const existingSize = existingItem ? this.estimateItemSize(existingItem) : 0;
            
            if ((currentSize - existingSize + estimatedSize) > this.maxSize) {
                throw new Error('Storage quota exceeded');
            }
        }

        this.store.set(safeKey, item);
    }

    protected async removeStoredItem(key: string): Promise<void> {
        this.store.delete(this.getSafeKey(key));
    }

    protected async clearStorage(): Promise<void> {
        this.store.clear();
    }

    protected async getStorageKeys(): Promise<string[]> {
        return Array.from(this.store.keys());
    }

    protected async getStorageLength(): Promise<number> {
        return this.store.size;
    }

    async getUsage(): Promise<{ used: number; available: number; total: number }> {
        const used = this.getCurrentSize();
        const total = this.maxSize;
        const available = total > 0 ? Math.max(0, total - used) : -1;

        return { used, available, total };
    }

    getCapabilities(): StorageCapabilities {
        return {
            supportsTTL: true, // We implement TTL ourselves
            supportsTransactions: false, // Memory storage doesn't support transactions
            supportsBulkOperations: true, // We can implement bulk operations efficiently
            maxStorageSize: this.maxSize,
            persistent: false, // Memory storage is not persistent
            platform: 'memory'
        };
    }

    async dispose(): Promise<void> {
        await super.dispose();
        this.store.clear();
    }

    /**
     * Get the current size of stored data (estimated)
     */
    private getCurrentSize(): number {
        let totalSize = 0;

        for (const [key, value] of this.store) {
            totalSize += this.estimateItemSize(value);
        }

        return totalSize;
    }

    /**
     * Estimate the size of an item in bytes
     */
    private estimateItemSize(item: StoredItemWithTimestamp<any>): number {
        try {
            // Simple estimation using JSON string length
            // This is not perfectly accurate but good enough for memory management
            const jsonString = JSON.stringify(item);
            return jsonString.length * 2; // Multiply by 2 to account for UTF-16 encoding
        } catch (error) {
            // If JSON.stringify fails, return a default size
            return 1024; // 1KB default
        }
    }

    /**
     * Get all stored items (useful for debugging)
     */
    getAllItems(): Map<string, StoredItemWithTimestamp<any>> {
        return new Map(this.store);
    }

    /**
     * Set the maximum storage size
     */
    setMaxSize(maxSize: number): void {
        this.maxSize = maxSize;
    }

    /**
     * Get the maximum storage size
     */
    getMaxSize(): number {
        return this.maxSize;
    }

    /**
     * Get storage statistics
     */
    getStats(): {
        itemCount: number;
        totalSize: number;
        maxSize: number;
        utilizationPercentage: number;
    } {
        const itemCount = this.store.size;
        const totalSize = this.getCurrentSize();
        const maxSize = this.maxSize;
        const utilizationPercentage = maxSize > 0 ? (totalSize / maxSize) * 100 : 0;

        return {
            itemCount,
            totalSize,
            maxSize,
            utilizationPercentage
        };
    }

    /**
     * Bulk set operation
     */
    async setBulk<T>(items: Array<{ key: string; value: T; ttl?: number }>): Promise<void> {
        for (const item of items) {
            await this.setItem(item.key, item.value, item.ttl);
        }
    }

    /**
     * Bulk get operation
     */
    async getBulk<T>(keys: string[]): Promise<Array<{ key: string; value: T | null }>> {
        const results: Array<{ key: string; value: T | null }> = [];
        
        for (const key of keys) {
            const value = await this.getItem<T>(key);
            results.push({ key, value });
        }
        
        return results;
    }

    /**
     * Bulk remove operation
     */
    async removeBulk(keys: string[]): Promise<void> {
        for (const key of keys) {
            await this.removeItem(key);
        }
    }

    /**
     * Find items by key pattern (useful for debugging and testing)
     */
    async findByPattern(pattern: RegExp): Promise<Array<{ key: string; value: any }>> {
        const results: Array<{ key: string; value: any }> = [];
        
        for (const [key, item] of this.store) {
            if (pattern.test(key)) {
                // Check if item is expired
                if (!this.isItemExpired(item)) {
                    results.push({ key, value: item.data });
                }
            }
        }
        
        return results;
    }

    /**
     * Get items that will expire within a certain time frame
     */
    async getExpiringItems(withinMs: number): Promise<Array<{ key: string; expiresAt: number }>> {
        const now = Date.now();
        const threshold = now + withinMs;
        const results: Array<{ key: string; expiresAt: number }> = [];
        
        for (const [key, item] of this.store) {
            if (item.ttl && item.ttl > 0) {
                const expiresAt = item.timestamp + item.ttl;
                if (expiresAt <= threshold && expiresAt > now) {
                    results.push({ key, expiresAt });
                }
            }
        }
        
        return results;
    }

    /**
     * Force garbage collection of expired items
     */
    async forceCleanup(): Promise<number> {
        return await this.cleanup();
    }
}
