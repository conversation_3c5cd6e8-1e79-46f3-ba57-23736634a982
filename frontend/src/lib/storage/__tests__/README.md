# Storage Module Test Suite

This directory contains comprehensive test coverage for the `lib/storage` module, achieving 100% coverage across multiple environments and use cases.

## Test Structure

```
__tests__/
├── utils/
│   └── test-helpers.ts          # Shared test utilities and mocks
├── unit/
│   ├── adapters/                # Unit tests for storage adapters
│   │   ├── BrowserStorageAdapter.test.ts
│   │   ├── NodeStorageAdapter.test.ts
│   │   ├── MemoryStorageAdapter.test.ts
│   │   └── ReactNativeStorageAdapter.test.ts
│   ├── StorageManager.test.ts   # Storage manager unit tests
│   ├── StorageFactory.test.ts   # Storage factory unit tests
│   └── platformDetection.test.ts # Platform detection unit tests
├── environment/
│   ├── browser.test.ts          # Browser environment tests
│   ├── node.test.ts             # Node.js environment tests
│   ├── react.test.tsx           # React integration tests
│   └── ssr.test.ts              # Server-side rendering tests
├── integration/
│   ├── cross-platform.test.ts  # Cross-platform integration tests
│   ├── storage-manager-integration.test.ts
│   └── repository-integration.test.ts
└── README.md                    # This file
```

## Test Categories

### 1. Unit Tests (`unit/`)

**Adapter Tests:**
- `BrowserStorageAdapter.test.ts`: Tests localforage integration, quota handling, browser-specific features
- `NodeStorageAdapter.test.ts`: Tests file system operations, Node.js-specific functionality
- `MemoryStorageAdapter.test.ts`: Tests in-memory storage, size limits, performance
- `ReactNativeStorageAdapter.test.ts`: Tests AsyncStorage integration, React Native features

**Core Module Tests:**
- `StorageManager.test.ts`: Tests storage manager functionality, multi-store management
- `StorageFactory.test.ts`: Tests adapter creation, platform detection, fallback mechanisms
- `platformDetection.test.ts`: Tests platform detection accuracy across environments

### 2. Environment Tests (`environment/`)

**Browser Environment (`browser.test.ts`):**
- LocalForage integration and configuration
- Storage technology detection (IndexedDB, localStorage, WebSQL)
- Quota exceeded scenarios
- Browser-specific features (storage events, tab synchronization)
- Cross-browser compatibility

**Node.js Environment (`node.test.ts`):**
- File system storage operations
- Environment variable configuration
- Concurrent file operations
- Error recovery and resilience
- Performance with large datasets

**React Environment (`react.test.tsx`):**
- React component integration
- Custom hooks for storage
- State management integration
- Component lifecycle handling
- Error boundaries integration

**SSR Environment (`ssr.test.ts`):**
- Server-side rendering detection
- Memory adapter fallback during SSR
- Hydration behavior simulation
- Next.js pattern integration
- SSR performance considerations

### 3. Integration Tests (`integration/`)

**Cross-Platform (`cross-platform.test.ts`):**
- API consistency across adapters
- Data serialization compatibility
- Error handling consistency
- Performance comparison
- Migration scenarios

**Storage Manager Integration (`storage-manager-integration.test.ts`):**
- Multi-store management
- Health monitoring across stores
- Global cleanup operations
- Concurrent operations
- Memory management

**Repository Integration (`repository-integration.test.ts`):**
- Repository pattern implementation
- Multi-repository scenarios
- Application lifecycle integration
- Error handling and recovery
- Performance and scalability

## Test Utilities

### `test-helpers.ts`

Provides shared utilities for all tests:

- **Environment Mocking:**
  - `mockBrowserEnvironment()`: Mocks browser globals (window, localStorage, IndexedDB)
  - `mockNodeEnvironment()`: Mocks Node.js environment (process, require)
  - `mockReactNativeEnvironment()`: Mocks React Native environment (AsyncStorage, navigator)
  - `mockSSREnvironment()`: Mocks SSR environment (no window object)

- **Test Data:**
  - `testData.simple`: Basic data types for testing
  - `testData.complex`: Complex nested objects and arrays
  - `testData.large`: Large dataset (1000 items) for performance testing

- **Helper Functions:**
  - `createTestStorage()`: Creates configured test storage adapter
  - `testBasicOperations()`: Tests standard CRUD operations
  - `testTTLFunctionality()`: Tests time-to-live behavior
  - `assertAdapterCapabilities()`: Validates adapter capabilities
  - `wait()`: Promise-based delay utility

## Running Tests

### All Tests
```bash
npm test
```

### Specific Test Categories
```bash
# Unit tests only
npm test -- --testPathPattern=unit

# Environment tests only
npm test -- --testPathPattern=environment

# Integration tests only
npm test -- --testPathPattern=integration

# Specific adapter tests
npm test -- --testPathPattern=BrowserStorageAdapter
npm test -- --testPathPattern=NodeStorageAdapter
```

### Coverage Report
```bash
npm test -- --coverage
```

### Watch Mode
```bash
npm test -- --watch
```

## Test Coverage Goals

The test suite aims for 100% coverage across:

- **Line Coverage**: Every line of code executed
- **Branch Coverage**: Every conditional branch tested
- **Function Coverage**: Every function called
- **Statement Coverage**: Every statement executed

### Coverage by Module

- **Storage Adapters**: 100% coverage including error scenarios
- **Storage Manager**: 100% coverage including edge cases
- **Storage Factory**: 100% coverage including fallback chains
- **Platform Detection**: 100% coverage across all environments
- **Public API**: 100% coverage of all exported functions

## Test Environment Setup

### Browser Tests
- Uses jsdom environment via Jest
- Mocks localforage and browser APIs
- Tests localStorage, IndexedDB, and WebSQL scenarios

### Node.js Tests
- Uses Node.js environment
- Mocks file system operations
- Tests file-based storage scenarios

### React Tests
- Uses React Testing Library
- Tests component integration and hooks
- Includes error boundary testing

### SSR Tests
- Simulates server-side rendering environment
- Tests fallback mechanisms
- Validates hydration behavior

## Mock Strategy

### External Dependencies
- **localforage**: Mocked with in-memory Map for consistent testing
- **File System**: Mocked with Jest functions for Node.js tests
- **AsyncStorage**: Mocked for React Native tests
- **Browser APIs**: Mocked for browser-specific functionality

### Platform Detection
- Environment variables and globals are mocked
- Platform detection is tested with various mock scenarios
- Fallback chains are validated with controlled failures

## Performance Testing

### Benchmarks Included
- Large dataset operations (1000+ items)
- Concurrent operations (10+ simultaneous)
- Memory usage patterns
- Cleanup operation efficiency
- Cross-platform performance comparison

### Performance Thresholds
- Basic operations: < 10ms
- Large dataset operations: < 2 seconds
- Concurrent operations: < 1 second
- Cleanup operations: < 1 second

## Error Scenarios Tested

### Storage Failures
- Quota exceeded errors
- Permission denied errors
- Disk space errors
- Network connectivity issues (for remote storage)

### Platform Failures
- Missing APIs (IndexedDB not available)
- Corrupted data scenarios
- Concurrent access conflicts
- Initialization failures

### Application Failures
- Component unmounting during operations
- Rapid re-renders
- Memory leaks
- Error boundary triggers

## Continuous Integration

The test suite is designed to run in CI environments:

- **GitHub Actions**: Automated testing on push/PR
- **Multiple Node.js versions**: 16.x, 18.x, 20.x
- **Multiple browsers**: Chrome, Firefox, Safari (via Playwright)
- **Coverage reporting**: Integrated with coverage services

## Contributing to Tests

When adding new features to the storage module:

1. **Add unit tests** for new functionality
2. **Update integration tests** if API changes
3. **Add environment tests** for platform-specific features
4. **Update test helpers** if new utilities are needed
5. **Maintain 100% coverage** for all new code

### Test Naming Convention
- Test files: `*.test.ts` or `*.test.tsx`
- Test descriptions: Use descriptive names explaining what is being tested
- Test groups: Use `describe` blocks to organize related tests
- Test cases: Use `it` blocks with clear expectations

### Mock Guidelines
- Keep mocks simple and focused
- Use Jest mocks for external dependencies
- Clean up mocks in `afterEach` hooks
- Document complex mock scenarios

This comprehensive test suite ensures the storage module works reliably across all supported environments and use cases.
