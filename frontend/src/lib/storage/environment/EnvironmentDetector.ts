/**
 * Environment detection abstraction layer.
 * 
 * This module provides an abstraction layer for environment detection
 * that can be easily mocked and tested. It separates the concerns of
 * environment detection from the storage logic.
 */

import type { PlatformInfo } from '../types';

/**
 * Interface for environment detection
 */
export interface IEnvironmentDetector {
    detectPlatform(): PlatformInfo;
    isBrowser(): boolean;
    isNode(): boolean;
    isReactNative(): boolean;
    isSSR(): boolean;
    isLocalStorageAvailable(): boolean;
    isIndexedDBAvailable(): boolean;
    isWebSQLAvailable(): boolean;
    isAsyncStorageAvailable(): boolean;
    getRecommendedStorageAdapter(): string;
}

/**
 * Default environment detector implementation
 */
export class DefaultEnvironmentDetector implements IEnvironmentDetector {
    detectPlatform(): PlatformInfo {
        const info: PlatformInfo = {
            platform: 'unknown',
            isServer: false,
            isBrowser: false,
            isReactNative: false,
            details: {}
        };

        // Check for React Native first (it has some browser-like globals)
        if (this.isReactNative()) {
            info.platform = 'react-native';
            info.isReactNative = true;
            info.details.reactNativeVersion = this.getReactNativeVersion();
            return info;
        }

        // Check for browser environment
        if (this.isBrowser()) {
            info.platform = 'browser';
            info.isBrowser = true;
            info.details.userAgent = this.getUserAgent();
            return info;
        }

        // Check for Node.js environment
        if (this.isNode()) {
            info.platform = 'node';
            info.isServer = true;
            info.details.nodeVersion = this.getNodeVersion();
            info.details.isSSR = this.isSSR();
            return info;
        }

        return info;
    }

    isBrowser(): boolean {
        return typeof window !== 'undefined' && 
               typeof window.document !== 'undefined' &&
               typeof window.localStorage !== 'undefined';
    }

    isNode(): boolean {
        return typeof process !== 'undefined' && 
               process.versions != null && 
               process.versions.node != null &&
               typeof window === 'undefined';
    }

    isReactNative(): boolean {
        return (typeof navigator !== 'undefined' &&
                typeof navigator.userAgent === 'string' &&
                navigator.userAgent.includes('ReactNative')) ||
               (typeof global !== 'undefined' &&
                typeof (global as any).HermesInternal !== 'undefined') ||
               (typeof require !== 'undefined' &&
                typeof global !== 'undefined' &&
                (() => {
                    try {
                        if (typeof (global as any).HermesInternal !== 'undefined' ||
                            typeof (global as any).__METRO__ !== 'undefined') {
                            return require('react-native') !== undefined;
                        }
                        return false;
                    } catch {
                        return false;
                    }
                })());
    }

    isSSR(): boolean {
        return typeof window === 'undefined' && 
               typeof global !== 'undefined';
    }

    isLocalStorageAvailable(): boolean {
        try {
            if (typeof window === 'undefined' || !window.localStorage) {
                return false;
            }

            const testKey = '__localStorage_test__';
            window.localStorage.setItem(testKey, 'test');
            window.localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            return false;
        }
    }

    isIndexedDBAvailable(): boolean {
        try {
            return typeof window !== 'undefined' && 
                   'indexedDB' in window && 
                   window.indexedDB !== null;
        } catch (error) {
            return false;
        }
    }

    isWebSQLAvailable(): boolean {
        try {
            return typeof window !== 'undefined' && 
                   'openDatabase' in window;
        } catch (error) {
            return false;
        }
    }

    isAsyncStorageAvailable(): boolean {
        try {
            return this.isReactNative() && 
                   typeof require !== 'undefined';
        } catch (error) {
            return false;
        }
    }

    getRecommendedStorageAdapter(): string {
        const platform = this.detectPlatform();

        switch (platform.platform) {
            case 'browser':
                if (this.isIndexedDBAvailable()) return 'browser';
                if (this.isLocalStorageAvailable()) return 'browser';
                return 'memory';

            case 'node':
                return 'node';

            case 'react-native':
                return 'react-native';

            default:
                return 'memory';
        }
    }

    private getUserAgent(): string | undefined {
        try {
            return typeof navigator !== 'undefined' ? navigator.userAgent : undefined;
        } catch (error) {
            return undefined;
        }
    }

    private getNodeVersion(): string | undefined {
        try {
            return typeof process !== 'undefined' ? process.version : undefined;
        } catch (error) {
            return undefined;
        }
    }

    private getReactNativeVersion(): string | undefined {
        try {
            // Try to get version from Platform.constants
            if (typeof require !== 'undefined') {
                try {
                    const Platform = require('react-native').Platform;
                    if (Platform?.constants?.reactNativeVersion) {
                        const version = Platform.constants.reactNativeVersion;
                        return `${version.major}.${version.minor}.${version.patch}${version.prerelease ? `-${version.prerelease}` : ''}`;
                    }
                } catch {
                    // Platform not available, try other methods
                }

                // Try to get version from package.json
                try {
                    const packageJson = require('react-native/package.json');
                    if (packageJson?.version) {
                        return packageJson.version;
                    }
                } catch {
                    // Package.json not available
                }
            }

            // Try to get version from global variables
            if (typeof global !== 'undefined') {
                const globalObj = global as any;
                if (globalObj.HermesInternal?.getRuntimeProperties) {
                    const hermesProps = globalObj.HermesInternal.getRuntimeProperties();
                    if (hermesProps?.['OSS Release Version']) {
                        return `hermes-${hermesProps['OSS Release Version']}`;
                    }
                }
            }

            // Try to parse from user agent
            if (typeof navigator !== 'undefined' && navigator.userAgent) {
                const userAgent = navigator.userAgent;
                const rnVersionMatch = userAgent.match(/ReactNative\/([0-9.]+)/);
                if (rnVersionMatch?.[1]) {
                    return rnVersionMatch[1];
                }
            }

            return undefined;
        } catch (error) {
            return undefined;
        }
    }
}

/**
 * Mock environment detector for testing
 */
export class MockEnvironmentDetector implements IEnvironmentDetector {
    private mockPlatform: PlatformInfo;
    private mockCapabilities: {
        isBrowser: boolean;
        isNode: boolean;
        isReactNative: boolean;
        isSSR: boolean;
        isLocalStorageAvailable: boolean;
        isIndexedDBAvailable: boolean;
        isWebSQLAvailable: boolean;
        isAsyncStorageAvailable: boolean;
    };

    constructor(
        platform: PlatformInfo,
        capabilities: Partial<MockEnvironmentDetector['mockCapabilities']> = {}
    ) {
        this.mockPlatform = platform;
        this.mockCapabilities = {
            isBrowser: false,
            isNode: false,
            isReactNative: false,
            isSSR: false,
            isLocalStorageAvailable: false,
            isIndexedDBAvailable: false,
            isWebSQLAvailable: false,
            isAsyncStorageAvailable: false,
            ...capabilities
        };
    }

    detectPlatform(): PlatformInfo {
        return this.mockPlatform;
    }

    isBrowser(): boolean {
        return this.mockCapabilities.isBrowser;
    }

    isNode(): boolean {
        return this.mockCapabilities.isNode;
    }

    isReactNative(): boolean {
        return this.mockCapabilities.isReactNative;
    }

    isSSR(): boolean {
        return this.mockCapabilities.isSSR;
    }

    isLocalStorageAvailable(): boolean {
        return this.mockCapabilities.isLocalStorageAvailable;
    }

    isIndexedDBAvailable(): boolean {
        return this.mockCapabilities.isIndexedDBAvailable;
    }

    isWebSQLAvailable(): boolean {
        return this.mockCapabilities.isWebSQLAvailable;
    }

    isAsyncStorageAvailable(): boolean {
        return this.mockCapabilities.isAsyncStorageAvailable;
    }

    getRecommendedStorageAdapter(): string {
        const platform = this.detectPlatform();
        return platform.platform === 'unknown' ? 'memory' : platform.platform;
    }
}

// Global environment detector instance
let environmentDetector: IEnvironmentDetector = new DefaultEnvironmentDetector();

/**
 * Get the current environment detector
 */
export function getEnvironmentDetector(): IEnvironmentDetector {
    return environmentDetector;
}

/**
 * Set a custom environment detector (useful for testing)
 */
export function setEnvironmentDetector(detector: IEnvironmentDetector): void {
    environmentDetector = detector;
}

/**
 * Reset to default environment detector
 */
export function resetEnvironmentDetector(): void {
    environmentDetector = new DefaultEnvironmentDetector();
}
