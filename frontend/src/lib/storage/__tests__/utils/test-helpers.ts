/**
 * Test utilities and helpers for storage module testing
 */

import { IStorageAdapter, StorageConfig } from '../../types';
import { MemoryStorageAdapter } from '../../adapters/MemoryStorageAdapter';

// Mock localforage with working storage
jest.mock('localforage', () => {
  // Global storage that persists across all instances
  const globalStorage = new Map<string, any>();

  const createMockInstance = (config?: any) => {
    // Create instance-specific prefix
    const instancePrefix = config ? `${config.name || 'default'}:${config.storeName || 'default'}:` : 'default:';

    return {
      ready: jest.fn(() => Promise.resolve()),
      getItem: jest.fn((key: string) => {
        const namespacedKey = instancePrefix + key;
        return Promise.resolve(globalStorage.get(namespacedKey) || null);
      }),
      setItem: jest.fn((key: string, value: any) => {
        const namespacedKey = instancePrefix + key;
        globalStorage.set(namespacedKey, value);
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key: string) => {
        const namespacedKey = instancePrefix + key;
        globalStorage.delete(namespacedKey);
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        // Clear only items for this instance
        const keysToDelete = Array.from(globalStorage.keys()).filter(key => key.startsWith(instancePrefix));
        keysToDelete.forEach(key => globalStorage.delete(key));
        return Promise.resolve();
      }),
      length: jest.fn(() => {
        const instanceKeys = Array.from(globalStorage.keys()).filter(key => key.startsWith(instancePrefix));
        return Promise.resolve(instanceKeys.length);
      }),
      keys: jest.fn(() => {
        const instanceKeys = Array.from(globalStorage.keys())
          .filter(key => key.startsWith(instancePrefix))
          .map(key => key.substring(instancePrefix.length));
        return Promise.resolve(instanceKeys);
      }),
      key: jest.fn((index: number) => {
        const instanceKeys = Array.from(globalStorage.keys())
          .filter(key => key.startsWith(instancePrefix))
          .map(key => key.substring(instancePrefix.length));
        return Promise.resolve(instanceKeys[index] || null);
      }),
      driver: jest.fn(() => 'localStorageWrapper'),
      config: jest.fn(() => config || {}),
      setDriver: jest.fn(() => Promise.resolve()),
      supports: jest.fn((driver: string) => {
        return ['asyncStorage', 'localStorageWrapper', 'webSQLStorage'].includes(driver);
      })
    };
  };

  const defaultInstance = createMockInstance();

  return {
    ...defaultInstance,
    INDEXEDDB: 'asyncStorage',
    LOCALSTORAGE: 'localStorageWrapper',
    WEBSQL: 'webSQLStorage',
    createInstance: jest.fn((config) => createMockInstance(config)),
    supports: jest.fn((driver: string) => {
      return ['asyncStorage', 'localStorageWrapper', 'webSQLStorage'].includes(driver);
    })
  };
});

/**
 * Create a test storage adapter with default configuration
 */
export async function createTestStorage(
  storeName: string = 'test_store',
  config?: Partial<StorageConfig>
): Promise<IStorageAdapter> {
  const adapter = new MemoryStorageAdapter();
  await adapter.initialize({
    name: 'test_db',
    storeName,
    ...config
  });
  return adapter;
}

/**
 * Mock browser environment globals
 */
export function mockBrowserEnvironment() {
  const originalWindow = global.window;
  const originalDocument = global.document;
  const originalNavigator = global.navigator;
  const originalLocalStorage = global.localStorage;
  const originalIndexedDB = global.indexedDB;

  // Create a mock storage that behaves like localStorage
  const mockStorageData: Record<string, string> = {};

  const mockStorage: Storage = {
    getItem: jest.fn((key: string): string | null => {
      return mockStorageData[key] || null;
    }),
    setItem: jest.fn((key: string, value: string): void => {
      mockStorageData[key] = String(value);
    }),
    removeItem: jest.fn((key: string): void => {
      delete mockStorageData[key];
    }),
    clear: jest.fn((): void => {
      Object.keys(mockStorageData).forEach(key => delete mockStorageData[key]);
    }),
    get length(): number {
      return Object.keys(mockStorageData).length;
    },
    key: jest.fn((index: number): string | null => {
      const keys = Object.keys(mockStorageData);
      return keys[index] || null;
    })
  };

  // Mock window object
  (global as any).window = {
    localStorage: mockStorage,
    indexedDB: {
      open: jest.fn().mockReturnValue({
        onsuccess: null,
        onerror: null,
        result: {
          createObjectStore: jest.fn(),
          transaction: jest.fn()
        }
      }),
      deleteDatabase: jest.fn(),
      cmp: jest.fn()
    },
    navigator: {
      userAgent: 'Mozilla/5.0 (Test Browser)',
      platform: 'Test',
      storage: {
        estimate: jest.fn().mockResolvedValue({
          usage: 1024,
          quota: 1024 * 1024 * 1024
        })
      }
    },
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  };

  // Mock document
  (global as any).document = {
    createElement: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    visibilityState: 'visible',
    hidden: false
  };

  // Mock navigator
  (global as any).navigator = {
    userAgent: 'Mozilla/5.0 (Test Browser)',
    platform: 'Test',
    storage: {
      estimate: jest.fn().mockResolvedValue({
        usage: 1024,
        quota: 1024 * 1024 * 1024
      })
    }
  };

  // Mock localStorage
  (global as any).localStorage = mockStorage;

  // Mock IndexedDB
  (global as any).indexedDB = {
    open: jest.fn().mockReturnValue({
      onsuccess: null,
      onerror: null,
      result: {
        createObjectStore: jest.fn(),
        transaction: jest.fn()
      }
    }),
    deleteDatabase: jest.fn(),
    cmp: jest.fn()
  };

  return () => {
    global.window = originalWindow;
    global.document = originalDocument;
    global.navigator = originalNavigator;
    global.localStorage = originalLocalStorage;
    global.indexedDB = originalIndexedDB;
  };
}

/**
 * Mock Node.js environment
 */
export function mockNodeEnvironment() {
  const originalProcess = global.process;
  const originalRequire = global.require;
  const originalWindow = global.window;
  const originalDocument = global.document;
  const originalNavigator = global.navigator;
  const originalLocalStorage = global.localStorage;
  const originalIndexedDB = global.indexedDB;

  // Remove browser globals to simulate Node.js environment
  (global as any).window = undefined;
  (global as any).document = undefined;
  (global as any).navigator = undefined;
  (global as any).localStorage = undefined;
  (global as any).indexedDB = undefined;

  // Mock process object
  (global as any).process = {
    versions: { node: '18.0.0' },
    env: { NODE_ENV: 'test' },
    cwd: jest.fn(() => '/test/directory'),
    platform: 'linux',
    version: 'v18.0.0'
  };

  // Mock require function
  (global as any).require = jest.fn();

  return () => {
    global.process = originalProcess;
    global.require = originalRequire;
    global.window = originalWindow;
    global.document = originalDocument;
    global.navigator = originalNavigator;
    global.localStorage = originalLocalStorage;
    global.indexedDB = originalIndexedDB;
  };
}

/**
 * Mock React Native environment
 */
export function mockReactNativeEnvironment() {
  const originalNavigator = global.navigator;
  const originalHermesInternal = (global as any).HermesInternal;
  const originalAsyncStorage = (global as any).AsyncStorage;
  const originalWindow = global.window;
  const originalDocument = global.document;
  const originalLocalStorage = global.localStorage;
  const originalIndexedDB = global.indexedDB;

  // Remove browser-specific globals
  (global as any).window = undefined;
  (global as any).document = undefined;
  (global as any).localStorage = undefined;
  (global as any).indexedDB = undefined;

  // Mock React Native navigator
  (global as any).navigator = {
    product: 'ReactNative',
    userAgent: 'ReactNative/0.72.0'
  };

  // Mock Hermes engine
  (global as any).HermesInternal = {
    getRuntimeProperties: () => ({
      'OSS Release Version': '0.12.0'
    })
  };

  // Mock AsyncStorage
  (global as any).AsyncStorage = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
    getAllKeys: jest.fn(),
    multiGet: jest.fn(),
    multiSet: jest.fn(),
    multiRemove: jest.fn()
  };

  return () => {
    global.navigator = originalNavigator;
    (global as any).HermesInternal = originalHermesInternal;
    (global as any).AsyncStorage = originalAsyncStorage;
    global.window = originalWindow;
    global.document = originalDocument;
    global.localStorage = originalLocalStorage;
    global.indexedDB = originalIndexedDB;
  };
}

/**
 * Mock SSR environment (no window object)
 */
export function mockSSREnvironment() {
  const originalWindow = global.window;
  const originalDocument = global.document;

  // Remove window and document to simulate SSR
  (global as any).window = undefined;
  (global as any).document = undefined;

  return () => {
    global.window = originalWindow;
    global.document = originalDocument;
  };
}

/**
 * Create mock file system for Node.js testing
 */
export function createMockFileSystem() {
  const mockFs = {
    promises: {
      mkdir: jest.fn().mockResolvedValue(undefined),
      access: jest.fn().mockResolvedValue(undefined),
      readFile: jest.fn().mockResolvedValue('{}'),
      writeFile: jest.fn().mockResolvedValue(undefined),
      unlink: jest.fn().mockResolvedValue(undefined),
      readdir: jest.fn().mockResolvedValue([])
    }
  };

  const mockPath = {
    join: jest.fn((...args) => args.join('/')),
    dirname: jest.fn((path) => path.split('/').slice(0, -1).join('/')),
    basename: jest.fn((path) => path.split('/').pop())
  };

  return { mockFs, mockPath };
}

/**
 * Wait for a specified amount of time
 */
export function wait(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Generate test data of various types
 */
export const testData = {
  simple: {
    string: 'test string',
    number: 42,
    boolean: true,
    null: null
  },
  complex: {
    array: [1, 2, 3, 'four', { five: 5 }],
    object: {
      nested: {
        deeply: {
          value: 'deep value'
        }
      }
    },
    date: new Date().toISOString(),
    mixed: {
      string: 'test',
      number: 123,
      boolean: false,
      array: ['a', 'b', 'c'],
      object: { key: 'value' }
    }
  },
  large: Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    data: `Item ${i}`,
    timestamp: Date.now() + i,
    metadata: {
      type: 'test',
      category: i % 10,
      tags: [`tag${i % 5}`, `category${i % 3}`]
    }
  }))
};

/**
 * Assert that an adapter has expected capabilities
 */
export function assertAdapterCapabilities(
  adapter: IStorageAdapter,
  expectedPlatform: string,
  expectedPersistent: boolean
) {
  const capabilities = adapter.getCapabilities();
  expect(capabilities.platform).toBe(expectedPlatform);
  expect(capabilities.persistent).toBe(expectedPersistent);
  expect(capabilities.supportsTTL).toBe(true);
  expect(typeof capabilities.maxStorageSize).toBe('number');
}

/**
 * Test basic storage operations
 */
export async function testBasicOperations(adapter: IStorageAdapter) {
  // Test setItem and getItem
  await adapter.setItem('test_key', 'test_value');
  const value = await adapter.getItem('test_key');
  expect(value).toBe('test_value');

  // Test complex data
  const complexData = { nested: { value: 42 }, array: [1, 2, 3] };
  await adapter.setItem('complex_key', complexData);
  const retrievedComplex = await adapter.getItem('complex_key');
  expect(retrievedComplex).toEqual(complexData);

  // Test removeItem
  await adapter.removeItem('test_key');
  const removedValue = await adapter.getItem('test_key');
  expect(removedValue).toBeNull();

  // Test keys and length
  await adapter.setItem('key1', 'value1');
  await adapter.setItem('key2', 'value2');
  
  const keys = await adapter.keys();
  expect(keys).toContain('key1');
  expect(keys).toContain('key2');
  expect(keys).toContain('complex_key');

  const length = await adapter.length();
  expect(length).toBe(3);

  // Test clear
  await adapter.clear();
  const finalLength = await adapter.length();
  expect(finalLength).toBe(0);
}

/**
 * Test TTL functionality
 */
export async function testTTLFunctionality(adapter: IStorageAdapter) {
  const shortTTL = 100; // 100ms
  
  // Set item with TTL
  await adapter.setItem('ttl_key', 'ttl_value', shortTTL);
  
  // Should be available immediately
  let value = await adapter.getItem('ttl_key');
  expect(value).toBe('ttl_value');
  
  // Wait for expiration
  await wait(150);
  
  // Should be expired now
  value = await adapter.getItem('ttl_key');
  expect(value).toBeNull();
}
