/**
 * Dependency Injection Architecture Tests
 * 
 * These tests demonstrate the new dependency injection architecture
 * and verify that it makes the storage system more testable.
 */

import { StorageAdapterFactory } from '../../StorageFactory';
import { getStorageContainer } from '../../di/StorageContainer';
import { 
    setupMockBrowserEnvironment,
    setupMockNodeEnvironment,
    setupMockSSREnvironment,
    createMockStorageAdapter
} from '../utils/di-test-helpers';

describe('Dependency Injection Architecture', () => {
    let cleanup: () => void;

    afterEach(() => {
        if (cleanup) {
            cleanup();
        }
    });

    describe('Browser Environment with DI', () => {
        beforeEach(() => {
            cleanup = setupMockBrowserEnvironment();
        });

        it('should create browser adapter with injected localforage', async () => {
            const factory = StorageAdapterFactory.getInstance();
            
            expect(factory.getAdapterType()).toBe('browser');
            expect(factory.isAdapterAvailable('browser')).toBe(true);

            const adapter = await factory.createAdapter({
                name: 'test_db',
                storeName: 'test_store'
            });

            expect(adapter).toBeDefined();
            expect(adapter.getCapabilities().platform).toBe('browser');
        });

        it('should use mocked localforage for storage operations', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createAdapter({
                name: 'test_db',
                storeName: 'test_store'
            });

            await adapter.setItem('test_key', 'test_value');
            const value = await adapter.getItem('test_key');
            
            expect(value).toBe('test_value');
        });
    });

    describe('Node.js Environment with DI', () => {
        beforeEach(() => {
            cleanup = setupMockNodeEnvironment();
        });

        it('should create node adapter with injected fs and path', async () => {
            const factory = StorageAdapterFactory.getInstance();
            
            expect(factory.getAdapterType()).toBe('node');
            expect(factory.isAdapterAvailable('node')).toBe(true);

            const adapter = await factory.createAdapter({
                name: 'test_db',
                storeName: 'test_store'
            });

            expect(adapter).toBeDefined();
            expect(adapter.getCapabilities().platform).toBe('node');
        });

        it('should use mocked fs for file operations', async () => {
            const factory = StorageAdapterFactory.getInstance();
            const adapter = await factory.createAdapter({
                name: 'test_db',
                storeName: 'test_store'
            });

            await adapter.setItem('test_key', 'test_value');
            const value = await adapter.getItem('test_key');
            
            expect(value).toBe('test_value');
        });
    });

    describe('SSR Environment with DI', () => {
        beforeEach(() => {
            cleanup = setupMockSSREnvironment();
        });

        it('should recommend memory adapter for SSR', async () => {
            const factory = StorageAdapterFactory.getInstance();
            
            // In SSR, should fall back to memory adapter
            expect(['memory', 'node'].includes(factory.getAdapterType())).toBe(true);
        });

        it('should create memory adapter when browser adapter is not available', async () => {
            const factory = StorageAdapterFactory.getInstance();
            
            // Browser adapter should not be available in SSR
            expect(factory.isAdapterAvailable('browser')).toBe(false);
            
            const adapter = await factory.createAdapterWithFallback({
                name: 'ssr_db',
                storeName: 'ssr_store'
            }, ['browser', 'memory']);

            expect(adapter).toBeDefined();
            expect(['memory', 'node'].includes(adapter.getCapabilities().platform)).toBe(true);
        });
    });

    describe('Container Management', () => {
        beforeEach(() => {
            cleanup = setupMockBrowserEnvironment();
        });

        it('should allow registering custom adapters', async () => {
            const container = getStorageContainer();
            const mockAdapter = createMockStorageAdapter();

            // Register a custom adapter
            container.registerAdapter('custom', () => mockAdapter);

            const adapter = await container.createAdapter('custom', {
                name: 'test_db',
                storeName: 'test_store'
            });

            expect(adapter).toBe(mockAdapter);
            expect(mockAdapter.initialize).toHaveBeenCalled();
        });

        it('should support singleton adapters', async () => {
            const container = getStorageContainer();
            
            const adapter1 = await container.createSingletonAdapter('memory', {
                name: 'test_db',
                storeName: 'test_store'
            });

            const adapter2 = await container.createSingletonAdapter('memory', {
                name: 'test_db',
                storeName: 'test_store'
            });

            // Should return the same instance
            expect(adapter1).toBe(adapter2);
        });

        it('should clean up singletons properly', async () => {
            const container = getStorageContainer();
            
            const adapter = await container.createSingletonAdapter('memory', {
                name: 'test_db',
                storeName: 'test_store'
            });

            await container.clearSingletons();
            
            // Should create a new instance after cleanup
            const newAdapter = await container.createSingletonAdapter('memory', {
                name: 'test_db',
                storeName: 'test_store'
            });

            expect(newAdapter).not.toBe(adapter);
        });
    });

    describe('Error Handling with DI', () => {
        beforeEach(() => {
            cleanup = setupMockBrowserEnvironment();
        });

        it('should handle missing dependencies gracefully', async () => {
            const container = getStorageContainer();
            
            // Try to create an adapter without required dependencies
            await expect(container.createAdapter('nonexistent', {
                name: 'test_db',
                storeName: 'test_store'
            })).rejects.toThrow('not registered');
        });

        it('should fall back to memory adapter when dependencies fail', async () => {
            const factory = StorageAdapterFactory.getInstance();
            
            // This should fall back to memory if browser adapter fails
            const adapter = await factory.createAdapter({
                name: 'test_db',
                storeName: 'test_store'
            });

            expect(adapter).toBeDefined();
        });
    });

    describe('Factory Reset', () => {
        it('should reset factory state properly', () => {
            const factory1 = StorageAdapterFactory.getInstance();
            StorageAdapterFactory.reset();
            const factory2 = StorageAdapterFactory.getInstance();

            // Should create a new instance
            expect(factory1).not.toBe(factory2);
        });
    });
});
