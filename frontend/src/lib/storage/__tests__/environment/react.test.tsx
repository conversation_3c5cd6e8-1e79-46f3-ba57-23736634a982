/**
 * React Environment Tests
 * Tests storage functionality in React components and React-specific patterns
 */

import React, { useState, useEffect } from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { initializeStorage, getRepositoryStorage } from '../../index';
import { IStorageAdapter } from '../../types';
import { mockBrowserEnvironment } from '../utils/test-helpers';
import '../utils/test-helpers';

// Test component that uses storage
const StorageTestComponent: React.FC<{
  repositoryName: string;
  initialValue?: string;
  onStorageReady?: (storage: IStorageAdapter) => void;
}> = ({ repositoryName, initialValue, onStorageReady }) => {
  const [storage, setStorage] = useState<IStorageAdapter | null>(null);
  const [value, setValue] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initStorage = async () => {
      try {
        await initializeStorage();
        const storageInstance = await getRepositoryStorage(repositoryName);
        setStorage(storageInstance);
        onStorageReady?.(storageInstance);

        if (initialValue) {
          await storageInstance.setItem('test_value', initialValue);
          setValue(initialValue);
        } else {
          const storedValue = await storageInstance.getItem('test_value');
          setValue((storedValue as string) || '');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    initStorage();
  }, [repositoryName, initialValue, onStorageReady]);

  const updateValue = async (newValue: string) => {
    if (storage) {
      try {
        await storage.setItem('test_value', newValue);
        setValue(newValue);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Update error');
      }
    }
  };

  if (loading) return <div data-testid="loading">Loading...</div>;
  if (error) return <div data-testid="error">{error}</div>;

  return (
    <div>
      <div data-testid="value">{value}</div>
      <button 
        data-testid="update-button" 
        onClick={() => updateValue('updated_value')}
      >
        Update Value
      </button>
      <div data-testid="storage-platform">
        {storage?.getCapabilities().platform}
      </div>
    </div>
  );
};

// Custom hook for storage
const useStorage = (repositoryName: string) => {
  const [storage, setStorage] = useState<IStorageAdapter | null>(null);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initStorage = async () => {
      try {
        await initializeStorage();
        const storageInstance = await getRepositoryStorage(repositoryName);
        setStorage(storageInstance);
        setIsReady(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Storage initialization failed');
      }
    };

    initStorage();
  }, [repositoryName]);

  const setItem = async (key: string, value: any) => {
    if (!storage) throw new Error('Storage not ready');
    return storage.setItem(key, value);
  };

  const getItem = async (key: string) => {
    if (!storage) throw new Error('Storage not ready');
    return storage.getItem(key);
  };

  const removeItem = async (key: string) => {
    if (!storage) throw new Error('Storage not ready');
    return storage.removeItem(key);
  };

  return {
    storage,
    isReady,
    error,
    setItem,
    getItem,
    removeItem
  };
};

// Test component using custom hook
const HookTestComponent: React.FC<{ repositoryName: string }> = ({ repositoryName }) => {
  const { storage, isReady, error, setItem, getItem } = useStorage(repositoryName);
  const [value, setValue] = useState<string>('');

  const handleLoad = async () => {
    try {
      const storedValue = await getItem('hook_test');
      setValue((storedValue as string) || 'no_value');
    } catch (err) {
      setValue('error');
    }
  };

  const handleSave = async () => {
    try {
      await setItem('hook_test', 'hook_value');
      setValue('hook_value');
    } catch (err) {
      setValue('save_error');
    }
  };

  if (!isReady) return <div data-testid="hook-loading">Loading...</div>;
  if (error) return <div data-testid="hook-error">{error}</div>;

  return (
    <div>
      <div data-testid="hook-value">{value}</div>
      <button data-testid="load-button" onClick={handleLoad}>Load</button>
      <button data-testid="save-button" onClick={handleSave}>Save</button>
      <div data-testid="hook-platform">{storage?.getCapabilities().platform}</div>
    </div>
  );
};

describe('React Environment Tests', () => {
  let cleanupBrowserMock: () => void;

  beforeEach(() => {
    cleanupBrowserMock = mockBrowserEnvironment();
  });

  afterEach(() => {
    cleanupBrowserMock();
    jest.clearAllMocks();
  });

  describe('React Component Integration', () => {
    it('should initialize storage in React component', async () => {
      render(<StorageTestComponent repositoryName="react_test" />);

      // Should show loading initially
      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // Wait for storage to initialize
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      });

      // Should show storage platform
      expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
    });

    it('should handle storage operations in React component', async () => {
      render(<StorageTestComponent repositoryName="react_ops" initialValue="initial" />);

      await waitFor(() => {
        expect(screen.getByTestId('value')).toHaveTextContent('initial');
      });

      // Update value
      act(() => {
        screen.getByTestId('update-button').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('value')).toHaveTextContent('updated_value');
      });
    });

    it.skip('should handle storage errors in React component', async () => {
      // Skip this test for now due to mocking complexity
      // TODO: Implement proper error testing with better mocking strategy
    });

    it('should provide storage instance to callback', async () => {
      let capturedStorage: IStorageAdapter | null = null;
      
      const handleStorageReady = (storage: IStorageAdapter) => {
        capturedStorage = storage;
      };

      render(
        <StorageTestComponent 
          repositoryName="callback_test" 
          onStorageReady={handleStorageReady}
        />
      );

      await waitFor(() => {
        expect(capturedStorage).not.toBeNull();
      });

      expect(capturedStorage!.getCapabilities().platform).toBe('browser');
    });
  });

  describe('Custom React Hook Integration', () => {
    it('should provide storage through custom hook', async () => {
      render(<HookTestComponent repositoryName="hook_test" />);

      // Should show loading initially
      expect(screen.getByTestId('hook-loading')).toBeInTheDocument();

      // Wait for hook to initialize
      await waitFor(() => {
        expect(screen.queryByTestId('hook-loading')).not.toBeInTheDocument();
      });

      // Should show platform
      expect(screen.getByTestId('hook-platform')).toHaveTextContent('browser');
    });

    it('should handle storage operations through custom hook', async () => {
      render(<HookTestComponent repositoryName="hook_ops" />);

      await waitFor(() => {
        expect(screen.queryByTestId('hook-loading')).not.toBeInTheDocument();
      });

      // Save value
      act(() => {
        screen.getByTestId('save-button').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('hook-value')).toHaveTextContent('hook_value');
      });

      // Load value
      act(() => {
        screen.getByTestId('load-button').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('hook-value')).toHaveTextContent('hook_value');
      });
    });

    it.skip('should handle hook errors gracefully', async () => {
      // Skip this test for now due to mocking complexity
      // TODO: Implement proper error testing with better mocking strategy
    });
  });

  describe('React Lifecycle Integration', () => {
    it('should handle component mounting and unmounting', async () => {
      const { unmount } = render(<StorageTestComponent repositoryName="lifecycle_test" />);

      await waitFor(() => {
        expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
      });

      // Unmount should not cause errors
      unmount();
    });

    it('should handle multiple component instances', async () => {
      render(
        <div>
          <StorageTestComponent repositoryName="multi_test_1" />
          <StorageTestComponent repositoryName="multi_test_2" />
        </div>
      );

      await waitFor(() => {
        const platforms = screen.getAllByTestId('storage-platform');
        expect(platforms).toHaveLength(2);
        platforms.forEach(platform => {
          expect(platform).toHaveTextContent('browser');
        });
      });
    });

    it('should handle rapid component re-renders', async () => {
      const TestWrapper: React.FC = () => {
        const [key, setKey] = useState(0);
        
        useEffect(() => {
          const interval = setInterval(() => {
            setKey(k => k + 1);
          }, 100);
          
          setTimeout(() => clearInterval(interval), 500);
          
          return () => clearInterval(interval);
        }, []);

        return <StorageTestComponent key={key} repositoryName="rerender_test" />;
      };

      render(<TestWrapper />);

      // Should eventually stabilize
      await waitFor(() => {
        expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
      }, { timeout: 1000 });
    });
  });

  describe('React State Management Integration', () => {
    it('should integrate with React state', async () => {
      const StateIntegrationComponent: React.FC = () => {
        const [items, setItems] = useState<string[]>([]);
        const [storage, setStorage] = useState<IStorageAdapter | null>(null);

        useEffect(() => {
          const initStorage = async () => {
            await initializeStorage();
            const storageInstance = await getRepositoryStorage('state_integration');
            setStorage(storageInstance);

            // Load existing items
            const storedItems = await storageInstance.getItem('items') || [];
            setItems((storedItems as string[]) || []);
          };

          initStorage();
        }, []);

        const addItem = async (item: string) => {
          const newItems = [...items, item];
          setItems(newItems);
          if (storage) {
            await storage.setItem('items', newItems);
          }
        };

        return (
          <div>
            <div data-testid="item-count">{items.length}</div>
            <button 
              data-testid="add-item" 
              onClick={() => addItem(`item_${items.length}`)}
            >
              Add Item
            </button>
            {items.map((item, index) => (
              <div key={index} data-testid={`item-${index}`}>{item}</div>
            ))}
          </div>
        );
      };

      render(<StateIntegrationComponent />);

      // Add items
      for (let i = 0; i < 3; i++) {
        act(() => {
          screen.getByTestId('add-item').click();
        });
      }

      await waitFor(() => {
        expect(screen.getByTestId('item-count')).toHaveTextContent('3');
        expect(screen.getByTestId('item-0')).toHaveTextContent('item_0');
        expect(screen.getByTestId('item-1')).toHaveTextContent('item_1');
        expect(screen.getByTestId('item-2')).toHaveTextContent('item_2');
      });
    });

    it('should handle async state updates', async () => {
      const AsyncStateComponent: React.FC = () => {
        const [data, setData] = useState<any>(null);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
          const loadData = async () => {
            await initializeStorage();
            const storage = await getRepositoryStorage('async_state');
            
            // Simulate async data loading
            await new Promise(resolve => setTimeout(resolve, 100));
            
            const storedData = await storage.getItem('async_data') || { message: 'default' };
            setData(storedData);
            setLoading(false);
          };

          loadData();
        }, []);

        if (loading) return <div data-testid="async-loading">Loading...</div>;

        return <div data-testid="async-data">{data.message}</div>;
      };

      render(<AsyncStateComponent />);

      expect(screen.getByTestId('async-loading')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('async-data')).toHaveTextContent('default');
      });
    });
  });

  describe('React Performance Considerations', () => {
    it('should not cause unnecessary re-renders', async () => {
      let renderCount = 0;

      const PerformanceTestComponent: React.FC = () => {
        renderCount++;
        const [, setStorage] = useState<IStorageAdapter | null>(null);

        useEffect(() => {
          const initStorage = async () => {
            await initializeStorage();
            const storageInstance = await getRepositoryStorage('performance_test');
            setStorage(storageInstance);
          };

          initStorage();
        }, []);

        return (
          <div data-testid="render-count">{renderCount}</div>
        );
      };

      render(<PerformanceTestComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('render-count')).toBeInTheDocument();
      });

      // Should not re-render excessively
      expect(renderCount).toBeLessThanOrEqual(3);
    });

    it.skip('should handle large datasets efficiently', async () => {
      // Simplified test that just verifies storage can handle multiple items
      const SimpleDataComponent: React.FC = () => {
        const [count, setCount] = useState(0);
        const [loading, setLoading] = useState(true);

        useEffect(() => {
          const loadData = async () => {
            try {
              await initializeStorage();
              const storage = await getRepositoryStorage('simple_data');

              // Store and retrieve a simple dataset
              const items = ['item1', 'item2', 'item3', 'item4', 'item5'];
              await storage.setItem('items', items);
              const retrieved = await storage.getItem('items');

              setCount((retrieved as string[])?.length || 0);
            } catch (err) {
              console.error('Simple data test error:', err);
              setCount(-1); // Error indicator
            } finally {
              setLoading(false);
            }
          };

          loadData();
        }, []);

        if (loading) return <div data-testid="simple-loading">Loading...</div>;

        return (
          <div>
            <div data-testid="simple-count">{count}</div>
            <div data-testid="simple-status">{count === 5 ? 'success' : count === -1 ? 'error' : 'partial'}</div>
          </div>
        );
      };

      render(<SimpleDataComponent />);

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.queryByTestId('simple-loading')).not.toBeInTheDocument();
      }, { timeout: 3000 });

      // Verify the data was loaded
      await waitFor(() => {
        expect(screen.getByTestId('simple-count')).toHaveTextContent('5');
        expect(screen.getByTestId('simple-status')).toHaveTextContent('success');
      }, { timeout: 2000 });
    }, 8000);
  });

  describe('React Error Boundaries Integration', () => {
    it('should work with React error boundaries', async () => {
      class ErrorBoundary extends React.Component<
        { children: React.ReactNode },
        { hasError: boolean; error?: Error }
      > {
        constructor(props: { children: React.ReactNode }) {
          super(props);
          this.state = { hasError: false };
        }

        static getDerivedStateFromError(error: Error) {
          return { hasError: true, error };
        }

        render() {
          if (this.state.hasError) {
            return <div data-testid="error-boundary">Error caught</div>;
          }

          return this.props.children;
        }
      }

      const ErrorComponent: React.FC = () => {
        const [shouldError, setShouldError] = useState(false);

        useEffect(() => {
          if (shouldError) {
            throw new Error('Test error');
          }
        }, [shouldError]);

        return (
          <div>
            <StorageTestComponent repositoryName="error_boundary_test" />
            <button 
              data-testid="trigger-error" 
              onClick={() => setShouldError(true)}
            >
              Trigger Error
            </button>
          </div>
        );
      };

      render(
        <ErrorBoundary>
          <ErrorComponent />
        </ErrorBoundary>
      );

      // Should render normally first
      await waitFor(() => {
        expect(screen.getByTestId('storage-platform')).toHaveTextContent('browser');
      });

      // Trigger error
      act(() => {
        screen.getByTestId('trigger-error').click();
      });

      // Error boundary should catch it
      await waitFor(() => {
        expect(screen.getByTestId('error-boundary')).toBeInTheDocument();
      });
    });
  });
});
