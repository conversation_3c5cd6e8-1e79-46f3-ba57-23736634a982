/**
 * Browser Environment Tests
 * Tests storage functionality specifically in browser runtime environment
 */

// Mock localforage for browser tests - must be at the top before imports
jest.mock('localforage', () => {
  // Shared storage for all instances - keyed by instance config
  const sharedStores = new Map<string, Map<string, any>>();

  const createMockInstance = (config?: any) => {
    // Create a unique key for this instance based on config
    const instanceKey = config ? `${config.name || 'default'}_${config.storeName || 'default'}` : 'default_default';

    // Get or create the store for this instance
    if (!sharedStores.has(instanceKey)) {
      sharedStores.set(instanceKey, new Map());
    }
    const mockStore = sharedStores.get(instanceKey)!;

    const mockInstance = {
      ready: jest.fn().mockResolvedValue(undefined),
      getItem: jest.fn((key) => Promise.resolve(mockStore.get(key) || null)),
      setItem: jest.fn((key, value) => {
        mockStore.set(key, value);
        return Promise.resolve(value);
      }),
      removeItem: jest.fn((key) => {
        mockStore.delete(key);
        return Promise.resolve();
      }),
      clear: jest.fn(() => {
        mockStore.clear();
        return Promise.resolve();
      }),
      keys: jest.fn(() => Promise.resolve(Array.from(mockStore.keys()))),
      length: jest.fn(() => Promise.resolve(mockStore.size)),
      driver: jest.fn(() => 'asyncStorage'),
      config: jest.fn(() => config || {}),
      setDriver: jest.fn(() => Promise.resolve()),
      // Store reference to the shared store for testing
      _mockStore: mockStore
    };

    return mockInstance;
  };

  const defaultInstance = createMockInstance();

  const mockLocalforage = {
    createInstance: jest.fn((config) => createMockInstance(config)),
    INDEXEDDB: 'asyncStorage',
    LOCALSTORAGE: 'localStorageWrapper',
    WEBSQL: 'webSQLStorage',
    supports: jest.fn(() => true),
    // Expose shared stores for testing
    _sharedStores: sharedStores,
    ...defaultInstance
  };

  return mockLocalforage;
});



import { StorageAdapterFactory } from '../../StorageFactory';
import { StorageManager } from '../../StorageManager';
import { detectPlatform, isBrowser, isLocalStorageAvailable, isIndexedDBAvailable, getRecommendedStorageAdapter } from '../../platformDetection';
import { mockBrowserEnvironment } from '../utils/test-helpers';

describe('Browser Environment Tests', () => {
  let cleanupBrowserMock: () => void;

  beforeEach(() => {
    cleanupBrowserMock = mockBrowserEnvironment();

    // Reset localforage mock to ensure clean state
    const localforage = require('localforage');
    jest.clearAllMocks();

    // Initialize shared stores if not present
    if (!localforage._sharedStores) {
      localforage._sharedStores = new Map();
    }

    // Clear shared stores for clean test state
    localforage._sharedStores.clear();

    // Ensure createInstance returns working mocks with shared storage
    localforage.createInstance.mockImplementation((config?: any) => {
      const instanceKey = config ? `${config.name || 'default'}_${config.storeName || 'default'}` : 'default_default';

      if (!localforage._sharedStores.has(instanceKey)) {
        localforage._sharedStores.set(instanceKey, new Map());
      }
      const mockStore = localforage._sharedStores.get(instanceKey);

      return {
        ready: jest.fn().mockResolvedValue(undefined),
        getItem: jest.fn((key) => Promise.resolve(mockStore.get(key) || null)),
        setItem: jest.fn((key, value) => {
          mockStore.set(key, value);
          return Promise.resolve(value);
        }),
        removeItem: jest.fn((key) => {
          mockStore.delete(key);
          return Promise.resolve();
        }),
        clear: jest.fn(() => {
          mockStore.clear();
          return Promise.resolve();
        }),
        keys: jest.fn(() => Promise.resolve(Array.from(mockStore.keys()))),
        length: jest.fn(() => Promise.resolve(mockStore.size)),
        driver: jest.fn(() => 'asyncStorage'),
        config: jest.fn(() => config || {}),
        setDriver: jest.fn(() => Promise.resolve()),
        _mockStore: mockStore
      };
    });
  });

  afterEach(() => {
    cleanupBrowserMock();
    jest.clearAllMocks();
  });

  describe('Platform Detection in Browser', () => {
    it('should correctly detect browser environment', () => {
      const isBrowserEnv = isBrowser();
      expect(isBrowserEnv).toBe(true);

      const platform = detectPlatform();
      expect(platform.platform).toBe('browser');
      expect(platform.isServer).toBe(false);
      expect(platform.isBrowser).toBe(true);
      expect(platform.isReactNative).toBe(false);
    });

    it('should recommend browser adapter', () => {
      const recommendedAdapter = getRecommendedStorageAdapter();
      expect(recommendedAdapter).toBe('browser');
    });

    it('should detect browser capabilities', () => {
      const platform = detectPlatform();
      expect(platform.details).toHaveProperty('userAgent');
      expect(platform.details.userAgent).toContain('Mozilla');
    });

    it('should detect localStorage availability', () => {
      const isAvailable = isLocalStorageAvailable();
      expect(isAvailable).toBe(true);
    });

    it('should detect IndexedDB availability', () => {
      const isAvailable = isIndexedDBAvailable();
      expect(isAvailable).toBe(true);
    });
  });

  describe('Storage Factory in Browser', () => {
    it('should create browser adapter by default', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapterType = factory.getAdapterType();
      expect(adapterType).toBe('browser');
    });

    it('should create browser adapter when specifically requested', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'test_browser_db',
        storeName: 'test_browser_store'
      });

      expect(adapter).toBeDefined();
      expect(adapter.getCapabilities().platform).toBe('browser');

      await adapter.dispose();
    });

    it('should report browser adapter as available', () => {
      const factory = StorageAdapterFactory.getInstance();
      const isAvailable = factory.isAdapterAvailable('browser');
      expect(isAvailable).toBe(true);

      const availableTypes = factory.getAvailableAdapterTypes();
      expect(availableTypes).toContain('browser');
    });

    it('should fallback to memory adapter if browser storage fails', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Mock localforage to fail
      const localforage = require('localforage');
      localforage.createInstance.mockReturnValue({
        ready: jest.fn().mockRejectedValue(new Error('Browser storage not available'))
      });

      const adapter = await factory.createAdapterWithFallback({
        name: 'fallback_test_db',
        storeName: 'fallback_test_store'
      }, ['browser', 'memory']);

      expect(adapter).toBeDefined();
      // Should fallback to memory adapter
      expect(adapter.getCapabilities().platform).toBe('memory');

      await adapter.dispose();
    });
  });

  describe('Storage Manager in Browser', () => {
    it('should initialize storage manager in browser environment', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'browser_test_db',
        version: 1,
        description: 'Browser test database'
      });

      expect(manager).toBeDefined();
      await manager.cleanup();
    });

    it('should create browser storage instances', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'browser_storage_db',
        version: 1
      });

      const storage = await manager.getStorage('browser_store');
      expect(storage).toBeDefined();
      // In test environment, it should create browser adapter successfully
      expect(['browser', 'memory']).toContain(storage.getCapabilities().platform);

      await manager.cleanup();
    });

    it('should handle multiple browser storage instances', async () => {
      const manager = new StorageManager();
      await manager.initialize({
        name: 'multi_browser_db',
        version: 1
      });

      const storage1 = await manager.getStorage('store1');
      const storage2 = await manager.getStorage('store2');

      expect(storage1).toBeDefined();
      expect(storage2).toBeDefined();
      expect(storage1).not.toBe(storage2);

      // Test health status
      const healthStatus = await manager.getHealthStatus();
      expect(healthStatus.healthy).toBe(true);
      expect(healthStatus.adapters).toHaveLength(2);

      await manager.cleanup();
    });
  });

  describe('LocalForage Integration', () => {
    it('should use localforage for browser storage', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'localforage_test_db',
        storeName: 'localforage_test_store'
      });

      await adapter.setItem('test_key', 'test_value');
      const value = await adapter.getItem('test_key');

      expect(value).toBe('test_value');

      // Verify localforage was used
      const localforage = require('localforage');
      expect(localforage.createInstance).toHaveBeenCalled();

      await adapter.dispose();
    });

    it('should configure localforage with custom options', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'custom_localforage_db',
        storeName: 'custom_localforage_store',
        version: 2,
        description: 'Custom localforage database',
        platformOptions: {
          driver: ['asyncStorage', 'localStorageWrapper']
        }
      });

      expect(adapter).toBeDefined();

      const localforage = require('localforage');
      expect(localforage.createInstance).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'custom_localforage_db',
          storeName: 'custom_localforage_store',
          version: 2,
          description: 'Custom localforage database',
          driver: ['asyncStorage', 'localStorageWrapper']
        })
      );

      await adapter.dispose();
    });

    it('should handle localforage driver preferences', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Test with IndexedDB preference
      const adapter1 = await factory.createSpecificAdapter('browser', {
        name: 'indexeddb_pref_db',
        storeName: 'indexeddb_pref_store',
        platformOptions: {
          driver: ['asyncStorage'] // IndexedDB
        }
      });

      expect(adapter1).toBeDefined();
      await adapter1.dispose();

      // Test with localStorage preference
      const adapter2 = await factory.createSpecificAdapter('browser', {
        name: 'localstorage_pref_db',
        storeName: 'localstorage_pref_store',
        platformOptions: {
          driver: ['localStorageWrapper'] // localStorage
        }
      });

      expect(adapter2).toBeDefined();
      await adapter2.dispose();
    });
  });

  describe('Browser Storage Limitations', () => {
    it('should handle quota exceeded scenarios', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'quota_test_db',
        storeName: 'quota_test_store'
      });

      // Mock quota exceeded error by overriding the adapter's setStoredItem method
      const quotaError = new Error('QuotaExceededError');
      quotaError.name = 'QuotaExceededError';

      // Access the private storeInstance and mock its setItem method
      const browserAdapter = adapter as any;
      const originalSetItem = browserAdapter.storeInstance.setItem;
      browserAdapter.storeInstance.setItem = jest.fn().mockRejectedValue(quotaError);

      await expect(adapter.setItem('large_item', 'x'.repeat(1000000))).rejects.toThrow('QuotaExceededError');

      // Restore original method
      browserAdapter.storeInstance.setItem = originalSetItem;
      await adapter.dispose();
    });

    it('should report correct storage capabilities', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'capabilities_test_db',
        storeName: 'capabilities_test_store'
      });

      const capabilities = adapter.getCapabilities();
      expect(capabilities.platform).toBe('browser');
      expect(capabilities.persistent).toBe(true);
      expect(capabilities.supportsTTL).toBe(true);
      expect(capabilities.maxStorageSize).toBeGreaterThan(0);

      await adapter.dispose();
    });

    it('should handle different storage technologies', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'tech_test_db',
        storeName: 'tech_test_store'
      });

      // Test that adapter can report current driver
      const browserAdapter = adapter as any; // Cast to access browser-specific methods
      const currentDriver = browserAdapter.getCurrentDriver();
      expect(typeof currentDriver).toBe('string');
      expect(['asyncStorage', 'localStorageWrapper', 'webSQLStorage']).toContain(currentDriver);

      await adapter.dispose();
    });
  });

  describe('Browser-Specific Features', () => {
    it('should handle page visibility changes', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'visibility_test_db',
        storeName: 'visibility_test_store'
      });

      await adapter.setItem('visibility_test', 'test_value');

      // Simulate page becoming hidden
      Object.defineProperty(document, 'hidden', {
        writable: true,
        value: true
      });

      // Storage should still work
      const value = await adapter.getItem('visibility_test');
      expect(value).toBe('test_value');

      await adapter.dispose();
    });

    it('should handle browser tab synchronization', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Create two adapter instances to simulate different tabs
      const adapter1 = await factory.createSpecificAdapter('browser', {
        name: 'sync_test_db',
        storeName: 'sync_test_store'
      });

      const adapter2 = await factory.createSpecificAdapter('browser', {
        name: 'sync_test_db',
        storeName: 'sync_test_store'
      });

      // Set data in first adapter
      await adapter1.setItem('sync_key', 'sync_value');

      // Should be available in second adapter (same storage)
      const value = await adapter2.getItem('sync_key');
      expect(value).toBe('sync_value');

      await adapter1.dispose();
      await adapter2.dispose();
    });

    it('should handle storage events', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'events_test_db',
        storeName: 'events_test_store'
      });

      // Mock storage event
      const storageEvent = new Event('storage');
      Object.defineProperty(storageEvent, 'key', { value: 'test_key' });
      Object.defineProperty(storageEvent, 'newValue', { value: 'new_value' });

      // Storage should continue to work during events
      await adapter.setItem('event_test', 'event_value');
      const value = await adapter.getItem('event_test');
      expect(value).toBe('event_value');

      await adapter.dispose();
    });
  });

  describe('Performance in Browser Environment', () => {
    it('should handle concurrent operations efficiently', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'concurrent_browser_db',
        storeName: 'concurrent_browser_store'
      });

      // Perform concurrent operations
      const operations = Array.from({ length: 10 }, async (_, i) => {
        await adapter.setItem(`key_${i}`, `value_${i}`);
        return adapter.getItem(`key_${i}`);
      });

      const results = await Promise.all(operations);

      results.forEach((result, i) => {
        expect(result).toBe(`value_${i}`);
      });

      await adapter.dispose();
    });

    it('should handle large datasets efficiently', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'large_browser_db',
        storeName: 'large_browser_store'
      });

      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({ id: i, data: `item_${i}` }));
      
      const startTime = Date.now();
      await adapter.setItem('large_dataset', largeDataset);
      const retrieved = await adapter.getItem('large_dataset');
      const endTime = Date.now();

      expect(retrieved).toHaveLength(1000);
      expect(endTime - startTime).toBeLessThan(2000); // Should complete within 2 seconds

      await adapter.dispose();
    });

    it('should optimize for browser memory usage', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'memory_opt_db',
        storeName: 'memory_opt_store'
      });

      // Add many small items
      for (let i = 0; i < 100; i++) {
        await adapter.setItem(`item_${i}`, `value_${i}`);
      }

      const length = await adapter.length();
      expect(length).toBe(100);

      // Cleanup should work efficiently
      const cleanedCount = await adapter.cleanup();
      expect(cleanedCount).toBeGreaterThanOrEqual(0);

      await adapter.dispose();
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from temporary browser storage errors', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'recovery_browser_db',
        storeName: 'recovery_browser_store'
      });

      // Access the private storeInstance and mock its setItem method with retry logic
      const browserAdapter = adapter as any;
      const originalSetItem = browserAdapter.storeInstance.setItem;

      let attemptCount = 0;
      browserAdapter.storeInstance.setItem = jest.fn().mockImplementation((key, value) => {
        attemptCount++;
        if (attemptCount === 1) {
          return Promise.reject(new Error('Temporary storage error'));
        }
        // Simulate successful retry
        return originalSetItem.call(browserAdapter.storeInstance, key, value);
      });

      // The adapter should handle the error and retry (this test simulates the retry behavior)
      try {
        await adapter.setItem('recovery_test', 'test_value');
        // If it succeeds on first try, that's also acceptable
        expect(attemptCount).toBeGreaterThanOrEqual(1);
      } catch (error) {
        // If it fails, we expect it to have tried at least once
        expect(attemptCount).toBeGreaterThanOrEqual(1);
        expect((error as Error).message).toContain('Temporary storage error');
      }

      // Restore original method
      browserAdapter.storeInstance.setItem = originalSetItem;
      await adapter.dispose();
    });

    it('should handle browser storage corruption gracefully', async () => {
      const factory = StorageAdapterFactory.getInstance();
      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'corruption_test_db',
        storeName: 'corruption_test_store'
      });

      const localforage = require('localforage');
      const mockInstance = localforage.createInstance();
      
      // Mock corrupted data
      mockInstance.getItem.mockResolvedValue('corrupted_json_data{');

      const value = await adapter.getItem('corrupted_key');
      expect(value).toBeNull(); // Should handle corruption gracefully

      await adapter.dispose();
    });

    it('should handle browser compatibility issues', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Mock browser without IndexedDB
      (global as any).window.indexedDB = undefined;

      const adapter = await factory.createSpecificAdapter('browser', {
        name: 'compat_test_db',
        storeName: 'compat_test_store'
      });

      expect(adapter).toBeDefined();
      // Should still work with localStorage fallback

      await adapter.dispose();
    });
  });

  describe('Cross-Browser Compatibility', () => {
    it('should work with different user agents', async () => {
      const userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15'
      ];

      for (const userAgent of userAgents) {
        // Mock navigator with configurable userAgent
        Object.defineProperty(global.navigator, 'userAgent', {
          value: userAgent,
          configurable: true,
          writable: true
        });

        const platform = detectPlatform();
        expect(platform.platform).toBe('browser');
        expect(platform.details.userAgent).toBe(userAgent);
      }
    });

    it('should handle different browser storage implementations', async () => {
      const factory = StorageAdapterFactory.getInstance();
      
      // Test with different driver configurations
      const driverConfigs = [
        ['asyncStorage'], // IndexedDB only
        ['localStorageWrapper'], // localStorage only
        ['asyncStorage', 'localStorageWrapper'], // IndexedDB with localStorage fallback
        ['localStorageWrapper', 'webSQLStorage'] // localStorage with WebSQL fallback
      ];

      for (const drivers of driverConfigs) {
        const adapter = await factory.createSpecificAdapter('browser', {
          name: 'driver_test_db',
          storeName: 'driver_test_store',
          platformOptions: { driver: drivers }
        });

        expect(adapter).toBeDefined();
        await adapter.setItem('driver_test', 'test_value');
        const value = await adapter.getItem('driver_test');
        expect(value).toBe('test_value');

        await adapter.dispose();
      }
    });
  });
});
